package org.example.test3;

/**
 * 结束节点位置修复测试程序
 * 确保结束节点排在流程图的最下方
 */
public class EndNodePositionFixTest {
    public static void main(String[] args) {
        System.out.println("🏁 结束节点位置修复测试程序");
        System.out.println("=".repeat(80));
        System.out.println("修复目标：");
        System.out.println("1. 结束节点不再排在流程中间");
        System.out.println("2. 结束节点强制移动到流程图最下方");
        System.out.println("3. 结束节点层级高于所有其他节点");
        System.out.println("4. 保持流程的逻辑顺序：开始 -> 中间 -> 结束");
        System.out.println();
        
        System.out.println("问题分析：");
        System.out.println("❌ 当前结束节点被排在流程中间位置");
        System.out.println("❌ 违反了流程图从上到下的逻辑顺序");
        System.out.println("❌ 用户体验不佳，难以识别流程终点");
        System.out.println();
        
        System.out.println("修复方案：");
        System.out.println("✅ 识别所有类型的结束节点");
        System.out.println("✅ 计算非结束节点的最大层级");
        System.out.println("✅ 强制将结束节点分配到最后层级");
        System.out.println("✅ 确保结束节点在视觉上位于最下方");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_endnode_fix.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行结束节点位置修复
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 结束节点位置修复文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ 结束节点层级最高：level_num > 所有其他节点");
                System.out.println("2. ✅ 结束节点Y坐标最大：位于流程图最下方");
                System.out.println("3. ✅ 流程逻辑正确：开始节点在上，结束节点在下");
                System.out.println("4. ✅ 视觉效果清晰：用户可以清楚看到流程终点");
                
                System.out.println();
                System.out.println("📋 预期修复效果：");
                System.out.println("修复前布局：");
                System.out.println("  Level 1: 开始节点");
                System.out.println("  Level 2: 中间节点");
                System.out.println("  Level 3: 结束节点 ❌ (在中间)");
                System.out.println("  Level 4: 其他节点");
                System.out.println("  Level 5: 更多节点");
                System.out.println();
                System.out.println("修复后布局：");
                System.out.println("  Level 1: 开始节点");
                System.out.println("  Level 2: 中间节点");
                System.out.println("  Level 3: 网关节点");
                System.out.println("  Level 4: 其他节点");
                System.out.println("  Level N: 结束节点 ✅ (在最下方)");
                
                System.out.println();
                System.out.println("📐 坐标验证：");
                System.out.println("- 结束节点应该有最大的Y坐标值");
                System.out.println("- Y坐标 = 150 + (level-1) * 400");
                System.out.println("- 如果level=10，则Y = 150 + 9*400 = 3750");
                
                System.out.println();
                System.out.println("🔍 调试信息验证：");
                System.out.println("- 检查控制台输出中的结束节点识别过程");
                System.out.println("- 确认结束节点的层级调整详情");
                System.out.println("- 验证最终层级分布的合理性");
                
            } else {
                System.err.println("❌ 结束节点位置修复文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 结束节点位置修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 结束节点识别逻辑错误");
            System.err.println("   2. 层级计算算法异常");
            System.err.println("   3. 出度入度分析问题");
            System.err.println("   4. 节点类型判断错误");
        }
    }
}
