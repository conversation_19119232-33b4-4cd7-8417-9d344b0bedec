package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 直线连接验证器
 * 验证特定连线是否已变成直线
 */
public class StraightLineVerifier {
    
    public static void main(String[] args) {
        System.out.println("📏 直线连接验证器");
        System.out.println("=".repeat(60));
        
        try {
            verifyStraightLines("src/main/java/org/example/test3/processDesignModel_straight_lines.json");
        } catch (Exception e) {
            System.err.println("❌ 验证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void verifyStraightLines(String filePath) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            System.out.println("⚠️ 文件不存在: " + filePath);
            return;
        }
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(file);
        
        // 收集节点和连线信息
        Map<String, NodeInfo> nodes = new HashMap<>();
        List<ConnectionInfo> connections = new ArrayList<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                NodeInfo node = parseNodeInfo(key, value);
                nodes.put(key, node);
            } else if (isConnectionObject(value)) {
                ConnectionInfo connection = parseConnectionInfo(key, value);
                if (connection != null) {
                    connections.add(connection);
                }
            }
        }
        
        System.out.println("📊 验证统计:");
        System.out.println("   - 节点数量: " + nodes.size());
        System.out.println("   - 连线数量: " + connections.size());
        
        // 验证目标连线
        verifyTargetConnections(connections, nodes);
        
        // 统计所有连线的路径点数量
        analyzeAllConnectionPaths(connections);
    }
    
    private static void verifyTargetConnections(List<ConnectionInfo> connections, Map<String, NodeInfo> nodes) {
        System.out.println("\n🎯 验证目标连线:");
        
        boolean foundStartToInit = false;
        boolean foundRecallToByStart = false;
        
        for (ConnectionInfo conn : connections) {
            NodeInfo sourceNode = nodes.get(conn.sourceId);
            NodeInfo targetNode = nodes.get(conn.targetId);
            
            if (sourceNode != null && targetNode != null) {
                // 检查流程开始->初始化连线
                if (isStartNode(sourceNode) && isInitNode(targetNode)) {
                    foundStartToInit = true;
                    verifyConnectionIsStraight("流程开始 -> 流程初始化", conn, sourceNode, targetNode);
                }
                
                // 检查重新提交->byStart连线
                if (isRecallNode(sourceNode) && isByStartNode(targetNode)) {
                    foundRecallToByStart = true;
                    verifyConnectionIsStraight("重新提交 -> byStart", conn, sourceNode, targetNode);
                }
            }
        }
        
        if (!foundStartToInit) {
            System.out.println("   ⚠️ 未找到流程开始->初始化连线");
        }
        
        if (!foundRecallToByStart) {
            System.out.println("   ⚠️ 未找到重新提交->byStart连线");
        }
    }
    
    private static void verifyConnectionIsStraight(String description, ConnectionInfo connection, 
                                                  NodeInfo sourceNode, NodeInfo targetNode) {
        System.out.println(String.format("\n📏 验证连线: %s", description));
        System.out.println(String.format("   连线ID: %s", connection.id));
        System.out.println(String.format("   源节点: %s (%s)", connection.sourceId, sourceNode.nodeName));
        System.out.println(String.format("   目标节点: %s (%s)", connection.targetId, targetNode.nodeName));
        
        if (connection.abspoints == null || connection.abspoints.isEmpty()) {
            System.out.println("   ❌ 无路径点信息");
            return;
        }
        
        System.out.println(String.format("   路径点数量: %d", connection.abspoints.size()));
        
        // 输出所有路径点
        for (int i = 0; i < connection.abspoints.size(); i++) {
            Point point = connection.abspoints.get(i);
            String pointType = "";
            if (i == 0) pointType = " (起点)";
            else if (i == connection.abspoints.size() - 1) pointType = " (终点)";
            else pointType = " (控制点)";
            
            System.out.println(String.format("      点%d: (%.1f, %.1f)%s", i + 1, point.x, point.y, pointType));
        }
        
        // 验证是否为直线
        if (connection.abspoints.size() == 2) {
            System.out.println("   ✅ 连线为直线（只有起点和终点）");
            
            Point start = connection.abspoints.get(0);
            Point end = connection.abspoints.get(1);
            
            // 检查连接点是否在节点边缘
            boolean startAtEdge = !isPointAtNodeCenter(start, sourceNode);
            boolean endAtEdge = !isPointAtNodeCenter(end, targetNode);
            
            System.out.println(String.format("   起点在节点边缘: %s", startAtEdge ? "是" : "否"));
            System.out.println(String.format("   终点在节点边缘: %s", endAtEdge ? "是" : "否"));
            
            // 计算直线距离
            double distance = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
            System.out.println(String.format("   直线距离: %.1f像素", distance));
            
        } else if (connection.abspoints.size() > 2) {
            System.out.println("   ❌ 连线不是直线（包含控制点）");
        } else {
            System.out.println("   ❌ 路径点数量异常");
        }
    }
    
    private static void analyzeAllConnectionPaths(List<ConnectionInfo> connections) {
        System.out.println("\n📊 所有连线路径点统计:");
        
        Map<Integer, Integer> pathPointCounts = new HashMap<>();
        
        for (ConnectionInfo conn : connections) {
            if (conn.abspoints != null) {
                int pointCount = conn.abspoints.size();
                pathPointCounts.put(pointCount, pathPointCounts.getOrDefault(pointCount, 0) + 1);
            }
        }
        
        pathPointCounts.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int pointCount = entry.getKey();
                int connectionCount = entry.getValue();
                String description = pointCount == 2 ? " (直线)" : pointCount > 2 ? " (弯曲)" : " (异常)";
                System.out.println(String.format("   %d个路径点: %d条连线%s", 
                    pointCount, connectionCount, description));
            });
    }
    
    private static boolean isPointAtNodeCenter(Point point, NodeInfo node) {
        // 允许10像素的误差
        return Math.abs(point.x - node.x) < 10 && Math.abs(point.y - node.y) < 10;
    }
    
    private static boolean isStartNode(NodeInfo node) {
        return (node.nodeType == 1) ||
               (node.nodeName != null && (node.nodeName.contains("开始") || node.nodeName.contains("start"))) ||
               (node.id != null && (node.id.contains("start") || node.id.contains("开始")));
    }
    
    private static boolean isInitNode(NodeInfo node) {
        return (node.nodeType == 2) ||
               (node.nodeName != null && (node.nodeName.contains("初始化") || node.nodeName.contains("init"))) ||
               (node.id != null && (node.id.contains("init") || node.id.contains("初始化")));
    }
    
    private static boolean isRecallNode(NodeInfo node) {
        return (node.nodeType == 4) ||
               (node.nodeName != null && (node.nodeName.contains("重新提交") || node.nodeName.contains("recall"))) ||
               (node.id != null && (node.id.contains("recall") || node.id.contains("重新提交")));
    }
    
    private static boolean isByStartNode(NodeInfo node) {
        return "byStart".equals(node.id) ||
               (node.nodeName != null && node.nodeName.contains("byStart"));
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private static NodeInfo parseNodeInfo(String id, JsonNode nodeJson) {
        NodeInfo info = new NodeInfo();
        info.id = id;
        
        if (nodeJson.has("nodeName")) {
            info.nodeName = nodeJson.get("nodeName").asText();
        }
        
        if (nodeJson.has("nodeType")) {
            info.nodeType = nodeJson.get("nodeType").asInt();
        }
        
        if (nodeJson.has("geometry")) {
            JsonNode geometry = nodeJson.get("geometry");
            if (geometry.has("x")) info.x = geometry.get("x").asDouble();
            if (geometry.has("y")) info.y = geometry.get("y").asDouble();
        }
        
        return info;
    }
    
    private static ConnectionInfo parseConnectionInfo(String id, JsonNode connJson) {
        ConnectionInfo info = new ConnectionInfo();
        info.id = id;
        
        // 解析source和target
        if (connJson.has("source") && connJson.get("source").has("id")) {
            info.sourceId = connJson.get("source").get("id").asText();
        }
        if (connJson.has("target") && connJson.get("target").has("id")) {
            info.targetId = connJson.get("target").get("id").asText();
        }
        
        // 解析几何信息
        if (connJson.has("geometry")) {
            JsonNode geometry = connJson.get("geometry");
            
            if (geometry.has("abspoints") && geometry.get("abspoints").isArray()) {
                JsonNode abspoints = geometry.get("abspoints");
                info.abspoints = new ArrayList<>();
                
                for (JsonNode pointNode : abspoints) {
                    if (pointNode.has("x") && pointNode.has("y")) {
                        info.abspoints.add(new Point(
                            pointNode.get("x").asDouble(),
                            pointNode.get("y").asDouble()
                        ));
                    }
                }
            }
        }
        
        return info.sourceId != null && info.targetId != null ? info : null;
    }
    
    // 数据结构
    static class NodeInfo {
        String id;
        String nodeName;
        int nodeType;
        double x, y;
    }
    
    static class ConnectionInfo {
        String id;
        String sourceId, targetId;
        List<Point> abspoints;
    }
    
    static class Point {
        double x, y;
        Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
    }
}
