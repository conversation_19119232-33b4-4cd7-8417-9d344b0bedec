package org.example.test3;

/**
 * 智能连线布局测试程序
 * 测试连线避让和路径优化功能
 */
public class SmartConnectionLayoutTest {
    public static void main(String[] args) {
        System.out.println("🔗 智能连线布局测试程序");
        System.out.println("=".repeat(80));
        System.out.println("测试目标：");
        System.out.println("1. 连线不再穿过节点中心");
        System.out.println("2. 多条连线避免重叠");
        System.out.println("3. 连线从节点边缘连接");
        System.out.println("4. 智能路径规划和避让");
        System.out.println();
        
        System.out.println("修复前问题：");
        System.out.println("❌ 连线直接穿过节点中心");
        System.out.println("❌ 多条连线重叠，难以区分");
        System.out.println("❌ 缺乏智能路径规划");
        System.out.println("❌ 视觉混乱，可读性差");
        System.out.println();
        
        System.out.println("修复后效果：");
        System.out.println("✅ 连线从节点边缘出发和到达");
        System.out.println("✅ 多条连线使用不同偏移，避免重叠");
        System.out.println("✅ 智能路径算法，包含弯曲和避让");
        System.out.println("✅ 清晰的视觉效果，提升可读性");
        System.out.println();
        
        System.out.println("连线类型和策略：");
        System.out.println("🔸 水平同层连线：右边缘 → 左边缘，Y偏移避让");
        System.out.println("🔸 垂直跨层连线：下边缘 → 上边缘，X偏移避让");
        System.out.println("🔸 对角线连线：智能选择最佳连接点");
        System.out.println("🔸 默认连线：中心点直连（备用方案）");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_smart_connections.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行智能连线布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 智能连线布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ 连线几何信息：检查JSON中的geometry字段");
                System.out.println("2. ✅ 起点终点坐标：startX/Y, endX/Y不在节点中心");
                System.out.println("3. ✅ 控制点信息：controlPoints数组包含路径控制点");
                System.out.println("4. ✅ 路径类型：pathType标识连线类型");
                
                System.out.println();
                System.out.println("📋 JSON结构验证：");
                System.out.println("连线对象应包含以下新字段：");
                System.out.println("```json");
                System.out.println("{");
                System.out.println("  \"geometry\": {");
                System.out.println("    \"startX\": 源节点边缘X坐标,");
                System.out.println("    \"startY\": 源节点边缘Y坐标,");
                System.out.println("    \"endX\": 目标节点边缘X坐标,");
                System.out.println("    \"endY\": 目标节点边缘Y坐标,");
                System.out.println("    \"controlPoints\": [控制点数组],");
                System.out.println("    \"pathType\": \"curved\"");
                System.out.println("  }");
                System.out.println("}");
                System.out.println("```");
                
                System.out.println();
                System.out.println("🔍 调试信息验证：");
                System.out.println("- 检查控制台输出中的'计算智能连线布局'部分");
                System.out.println("- 确认各种连线类型的识别和处理");
                System.out.println("- 验证连线路径计算的详细过程");
                
                System.out.println();
                System.out.println("📐 坐标计算验证：");
                System.out.println("- 起点坐标应在源节点边缘（非中心）");
                System.out.println("- 终点坐标应在目标节点边缘（非中心）");
                System.out.println("- 控制点提供弯曲路径，避免直线穿越");
                System.out.println("- 多条连线有不同的偏移量");
                
            } else {
                System.err.println("❌ 智能连线布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 智能连线布局测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 节点几何信息缺失或错误");
            System.err.println("   2. 连线对象结构不完整");
            System.err.println("   3. 路径计算算法异常");
            System.err.println("   4. JSON序列化问题");
        }
    }
}
