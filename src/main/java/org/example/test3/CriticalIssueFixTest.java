package org.example.test3;

/**
 * 关键问题修复测试程序
 * 专门测试网关节点独占层级和结束节点层级异常的修复
 */
public class CriticalIssueFixTest {
    public static void main(String[] args) {
        System.out.println("🔧 关键问题修复测试程序");
        System.out.println("=".repeat(80));
        System.out.println("修复目标：");
        System.out.println("1. 网关节点独占层级失效 -> 确保网关节点真正独占一行");
        System.out.println("2. 流程结束节点层级异常(1001) -> 修复为正常层级值");
        System.out.println("3. 层级调整后的坐标计算验证");
        System.out.println("4. 详细的调试输出和验证");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_critical_fix.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行关键问题修复后的布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 关键问题修复后的布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键修复验证要点：");
                System.out.println("1. ✅ 网关节点独占层级：同层级其他节点已推送到下方");
                System.out.println("2. ✅ 结束节点层级修复：从异常值1001修复为正常层级");
                System.out.println("3. ✅ 层级连续性验证：检查层级跳跃和冲突");
                System.out.println("4. ✅ 坐标计算验证：确保遵循SQL公式");
                
                System.out.println();
                System.out.println("📋 预期布局效果：");
                System.out.println("- 网关节点在其层级中完全独占一行");
                System.out.println("- 结束节点位于流程末端，具有合理的层级值");
                System.out.println("- 整体布局从左到右、从上到下清晰展示流程");
                System.out.println("- 所有节点坐标符合SQL存储过程的计算规则");
                
            } else {
                System.err.println("❌ 关键问题修复后的布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 关键问题修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 网关节点识别逻辑错误");
            System.err.println("   2. 结束节点层级计算异常");
            System.err.println("   3. 层级调整算法冲突");
            System.err.println("   4. 连接关系解析问题");
        }
    }
}
