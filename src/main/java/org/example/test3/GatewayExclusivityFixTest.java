package org.example.test3;

/**
 * 网关节点独占层级修复测试程序
 * 专门测试网关6和网关8等多个网关节点的层级冲突修复
 */
public class GatewayExclusivityFixTest {
    public static void main(String[] args) {
        System.out.println("🚪 网关节点独占层级修复测试程序");
        System.out.println("=".repeat(80));
        System.out.println("修复目标：");
        System.out.println("1. 网关6和网关8不再共享同一层级");
        System.out.println("2. 每个网关节点都独占一个层级行");
        System.out.println("3. 网关节点之间的层级连续且唯一");
        System.out.println("4. 验证JSON输出中的level_num值正确分离");
        System.out.println();
        
        System.out.println("问题分析：");
        System.out.println("❌ 当前网关6和网关8被排列在同一水平行");
        System.out.println("❌ adjustGatewayNodeLevels()方法存在多网关冲突处理缺陷");
        System.out.println("❌ 网关节点之间的层级冲突未被正确识别和解决");
        System.out.println();
        
        System.out.println("修复方案：");
        System.out.println("✅ 重新实现网关节点冲突检测逻辑");
        System.out.println("✅ 确保网关节点之间也不能共享层级");
        System.out.println("✅ 实现智能层级推送算法");
        System.out.println("✅ 添加最终验证机制");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_gateway_fix.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行网关节点独占层级修复
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 网关节点独占层级修复文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ 网关节点层级唯一性：每个网关节点独占一个层级");
                System.out.println("2. ✅ 网关节点垂直分离：网关6和网关8在不同的Y坐标");
                System.out.println("3. ✅ 层级连续性：网关节点层级连续分配");
                System.out.println("4. ✅ JSON数据正确性：level_num值正确分离");
                
                System.out.println();
                System.out.println("📋 预期修复效果：");
                System.out.println("修复前：");
                System.out.println("  Level 3: 网关6, 网关8 (❌ 共享层级)");
                System.out.println();
                System.out.println("修复后：");
                System.out.println("  Level 3: 网关6 (✅ 独占)");
                System.out.println("  Level 4: 网关8 (✅ 独占)");
                
                System.out.println();
                System.out.println("📐 坐标验证：");
                System.out.println("- 网关6: y = 150 + (3-1) * 400 = 950");
                System.out.println("- 网关8: y = 150 + (4-1) * 400 = 1350");
                System.out.println("- 垂直间距: 400像素");
                
                System.out.println();
                System.out.println("🔍 调试信息验证：");
                System.out.println("- 检查控制台输出中的网关节点处理过程");
                System.out.println("- 确认冲突检测和层级推送的详细步骤");
                System.out.println("- 验证最终独占性验证的结果");
                
            } else {
                System.err.println("❌ 网关节点独占层级修复文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 网关节点独占层级修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 网关节点识别逻辑错误");
            System.err.println("   2. 层级冲突检测算法缺陷");
            System.err.println("   3. 层级推送逻辑异常");
            System.err.println("   4. 递归调整算法问题");
        }
    }
}
