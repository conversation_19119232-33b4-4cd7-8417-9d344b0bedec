package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * mxGraph格式分析器
 * 分析JSON文件的mxGraph格式结构，特别是连线的几何信息
 */
public class MxGraphFormatAnalyzer {
    
    public static void main(String[] args) {
        System.out.println("📊 mxGraph格式分析器");
        System.out.println("=".repeat(80));
        
        try {
            analyzeMxGraphFormat("src/main/java/org/example/test3/processDesignModel.json");
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void analyzeMxGraphFormat(String filePath) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(filePath));
        
        System.out.println("🔍 JSON文件结构分析:");
        
        int totalObjects = 0;
        int nodeObjects = 0;
        int edgeObjects = 0;
        int objectsWithGeometry = 0;
        int objectsWithAbspoints = 0;
        
        List<EdgeGeometryInfo> edgeGeometries = new ArrayList<>();
        List<NodeGeometryInfo> nodeGeometries = new ArrayList<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            totalObjects++;
            
            if (value.has("geometry")) {
                objectsWithGeometry++;
                JsonNode geometry = value.get("geometry");
                
                if (geometry.has("abspoints")) {
                    objectsWithAbspoints++;
                }
            }
            
            // 分析节点对象
            if (isNodeObject(value)) {
                nodeObjects++;
                NodeGeometryInfo nodeInfo = analyzeNodeGeometry(key, value);
                if (nodeInfo != null) {
                    nodeGeometries.add(nodeInfo);
                }
            }
            
            // 分析边对象
            if (isEdgeObject(value)) {
                edgeObjects++;
                EdgeGeometryInfo edgeInfo = analyzeEdgeGeometry(key, value);
                if (edgeInfo != null) {
                    edgeGeometries.add(edgeInfo);
                }
            }
        }
        
        System.out.println("   📊 基本统计:");
        System.out.println("      - 总对象数: " + totalObjects);
        System.out.println("      - 节点对象: " + nodeObjects);
        System.out.println("      - 边对象: " + edgeObjects);
        System.out.println("      - 包含geometry的对象: " + objectsWithGeometry);
        System.out.println("      - 包含abspoints的对象: " + objectsWithAbspoints);
        
        // 分析节点几何格式
        analyzeNodeGeometryFormat(nodeGeometries);
        
        // 分析边几何格式
        analyzeEdgeGeometryFormat(edgeGeometries);
        
        // 输出mxGraph格式规范
        outputMxGraphSpecification();
    }
    
    private static NodeGeometryInfo analyzeNodeGeometry(String id, JsonNode nodeJson) {
        if (!nodeJson.has("geometry")) {
            return null;
        }
        
        NodeGeometryInfo info = new NodeGeometryInfo();
        info.id = id;
        
        JsonNode geometry = nodeJson.get("geometry");
        info.hasX = geometry.has("x");
        info.hasY = geometry.has("y");
        info.hasWidth = geometry.has("width");
        info.hasHeight = geometry.has("height");
        info.hasRelative = geometry.has("relative");
        
        if (info.hasX) info.x = geometry.get("x").asDouble();
        if (info.hasY) info.y = geometry.get("y").asDouble();
        if (info.hasWidth) info.width = geometry.get("width").asDouble();
        if (info.hasHeight) info.height = geometry.get("height").asDouble();
        if (info.hasRelative) info.relative = geometry.get("relative").asInt();
        
        return info;
    }
    
    private static EdgeGeometryInfo analyzeEdgeGeometry(String id, JsonNode edgeJson) {
        if (!edgeJson.has("geometry")) {
            return null;
        }
        
        EdgeGeometryInfo info = new EdgeGeometryInfo();
        info.id = id;
        
        JsonNode geometry = edgeJson.get("geometry");
        info.hasX = geometry.has("x");
        info.hasY = geometry.has("y");
        info.hasWidth = geometry.has("width");
        info.hasHeight = geometry.has("height");
        info.hasRelative = geometry.has("relative");
        info.hasAbspoints = geometry.has("abspoints");
        
        if (info.hasX) info.x = geometry.get("x").asDouble();
        if (info.hasY) info.y = geometry.get("y").asDouble();
        if (info.hasWidth) info.width = geometry.get("width").asDouble();
        if (info.hasHeight) info.height = geometry.get("height").asDouble();
        if (info.hasRelative) info.relative = geometry.get("relative").asInt();
        
        if (info.hasAbspoints) {
            JsonNode abspoints = geometry.get("abspoints");
            if (abspoints.isArray()) {
                info.abspointsCount = abspoints.size();
                
                // 解析前几个点
                for (int i = 0; i < Math.min(abspoints.size(), 5); i++) {
                    JsonNode point = abspoints.get(i);
                    if (point.has("x") && point.has("y")) {
                        info.samplePoints.add(new Point(
                            point.get("x").asDouble(),
                            point.get("y").asDouble()
                        ));
                    }
                }
            }
        }
        
        // 分析source和target
        if (edgeJson.has("source")) {
            JsonNode source = edgeJson.get("source");
            if (source.has("id")) {
                info.sourceId = source.get("id").asText();
            }
        }
        
        if (edgeJson.has("target")) {
            JsonNode target = edgeJson.get("target");
            if (target.has("id")) {
                info.targetId = target.get("id").asText();
            }
        }
        
        return info;
    }
    
    private static void analyzeNodeGeometryFormat(List<NodeGeometryInfo> nodeGeometries) {
        System.out.println("\n📐 节点几何格式分析:");
        
        if (nodeGeometries.isEmpty()) {
            System.out.println("   ⚠️ 未发现节点几何信息");
            return;
        }
        
        System.out.println("   📊 节点几何字段统计:");
        int withX = 0, withY = 0, withWidth = 0, withHeight = 0, withRelative = 0;
        
        for (NodeGeometryInfo info : nodeGeometries) {
            if (info.hasX) withX++;
            if (info.hasY) withY++;
            if (info.hasWidth) withWidth++;
            if (info.hasHeight) withHeight++;
            if (info.hasRelative) withRelative++;
        }
        
        System.out.println("      - x字段: " + withX + "/" + nodeGeometries.size());
        System.out.println("      - y字段: " + withY + "/" + nodeGeometries.size());
        System.out.println("      - width字段: " + withWidth + "/" + nodeGeometries.size());
        System.out.println("      - height字段: " + withHeight + "/" + nodeGeometries.size());
        System.out.println("      - relative字段: " + withRelative + "/" + nodeGeometries.size());
        
        // 显示示例
        System.out.println("\n   📋 节点几何示例（前3个）:");
        for (int i = 0; i < Math.min(nodeGeometries.size(), 3); i++) {
            NodeGeometryInfo info = nodeGeometries.get(i);
            System.out.println(String.format("      %s: x=%.1f, y=%.1f, w=%.1f, h=%.1f, rel=%d", 
                info.id, info.x, info.y, info.width, info.height, info.relative));
        }
    }
    
    private static void analyzeEdgeGeometryFormat(List<EdgeGeometryInfo> edgeGeometries) {
        System.out.println("\n🔗 边几何格式分析:");
        
        if (edgeGeometries.isEmpty()) {
            System.out.println("   ⚠️ 未发现边几何信息");
            return;
        }
        
        System.out.println("   📊 边几何字段统计:");
        int withX = 0, withY = 0, withWidth = 0, withHeight = 0, withRelative = 0, withAbspoints = 0;
        
        for (EdgeGeometryInfo info : edgeGeometries) {
            if (info.hasX) withX++;
            if (info.hasY) withY++;
            if (info.hasWidth) withWidth++;
            if (info.hasHeight) withHeight++;
            if (info.hasRelative) withRelative++;
            if (info.hasAbspoints) withAbspoints++;
        }
        
        System.out.println("      - x字段: " + withX + "/" + edgeGeometries.size());
        System.out.println("      - y字段: " + withY + "/" + edgeGeometries.size());
        System.out.println("      - width字段: " + withWidth + "/" + edgeGeometries.size());
        System.out.println("      - height字段: " + withHeight + "/" + edgeGeometries.size());
        System.out.println("      - relative字段: " + withRelative + "/" + edgeGeometries.size());
        System.out.println("      - abspoints字段: " + withAbspoints + "/" + edgeGeometries.size());
        
        // 显示详细的边几何示例
        System.out.println("\n   📋 边几何详细示例:");
        for (int i = 0; i < Math.min(edgeGeometries.size(), 3); i++) {
            EdgeGeometryInfo info = edgeGeometries.get(i);
            System.out.println(String.format("      边 %s (%s -> %s):", info.id, info.sourceId, info.targetId));
            System.out.println(String.format("         基础: x=%.1f, y=%.1f, w=%.1f, h=%.1f, rel=%d", 
                info.x, info.y, info.width, info.height, info.relative));
            System.out.println(String.format("         abspoints: %d个点", info.abspointsCount));
            
            if (!info.samplePoints.isEmpty()) {
                System.out.print("         路径点: ");
                for (Point p : info.samplePoints) {
                    System.out.print(String.format("(%.1f,%.1f) ", p.x, p.y));
                }
                System.out.println();
            }
        }
    }
    
    private static void outputMxGraphSpecification() {
        System.out.println("\n📚 mxGraph格式规范总结:");
        System.out.println("   🔸 节点几何格式:");
        System.out.println("      - x, y: 节点位置坐标");
        System.out.println("      - width, height: 节点尺寸");
        System.out.println("      - relative: 相对定位标志（通常为0）");
        
        System.out.println("\n   🔸 边几何格式:");
        System.out.println("      - x, y, width, height: 边界框信息（通常为0）");
        System.out.println("      - relative: 相对定位标志（通常为1）");
        System.out.println("      - abspoints: 绝对路径点数组");
        System.out.println("        * 每个点包含x, y坐标");
        System.out.println("        * 第一个点是起点，最后一个点是终点");
        System.out.println("        * 中间点是路径控制点");
        
        System.out.println("\n   🔸 推荐的边几何格式:");
        System.out.println("      ```json");
        System.out.println("      \"geometry\": {");
        System.out.println("        \"x\": 0,");
        System.out.println("        \"y\": 0,");
        System.out.println("        \"width\": 0,");
        System.out.println("        \"height\": 0,");
        System.out.println("        \"relative\": 1,");
        System.out.println("        \"abspoints\": [");
        System.out.println("          {\"x\": 起点X, \"y\": 起点Y},");
        System.out.println("          {\"x\": 控制点X, \"y\": 控制点Y},");
        System.out.println("          {\"x\": 终点X, \"y\": 终点Y}");
        System.out.println("        ]");
        System.out.println("      }");
        System.out.println("      ```");
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private static boolean isEdgeObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    // 数据结构
    static class NodeGeometryInfo {
        String id;
        boolean hasX, hasY, hasWidth, hasHeight, hasRelative;
        double x, y, width, height;
        int relative;
    }
    
    static class EdgeGeometryInfo {
        String id;
        String sourceId, targetId;
        boolean hasX, hasY, hasWidth, hasHeight, hasRelative, hasAbspoints;
        double x, y, width, height;
        int relative;
        int abspointsCount;
        List<Point> samplePoints = new ArrayList<>();
    }
    
    static class Point {
        double x, y;
        Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
    }
}
