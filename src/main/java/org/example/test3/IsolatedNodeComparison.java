package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 孤立节点对比分析器
 * 对比 NodeConnectionAnalyzer 和 FlowLayoutProcessor 的差异
 */
public class IsolatedNodeComparison {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    
    public static void main(String[] args) {
        System.out.println("🔍 孤立节点对比分析器");
        System.out.println("=".repeat(60));
        
        try {
            IsolatedNodeComparison analyzer = new IsolatedNodeComparison();
            analyzer.compareAnalysis();
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void compareAnalysis() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(INPUT_FILE));
        
        System.out.println("📊 方法1：NodeConnectionAnalyzer 的逻辑");
        Set<String> isolatedNodes1 = analyzeMethod1(rootNode);
        
        System.out.println("\n📊 方法2：FlowLayoutProcessor 的逻辑");
        Set<String> isolatedNodes2 = analyzeMethod2(rootNode);
        
        System.out.println("\n🔍 对比结果：");
        System.out.println("方法1检测到的孤立节点数: " + isolatedNodes1.size());
        System.out.println("方法2检测到的孤立节点数: " + isolatedNodes2.size());
        
        // 找出差异
        Set<String> onlyInMethod1 = new HashSet<>(isolatedNodes1);
        onlyInMethod1.removeAll(isolatedNodes2);
        
        Set<String> onlyInMethod2 = new HashSet<>(isolatedNodes2);
        onlyInMethod2.removeAll(isolatedNodes1);
        
        Set<String> common = new HashSet<>(isolatedNodes1);
        common.retainAll(isolatedNodes2);
        
        System.out.println("\n📋 详细对比：");
        System.out.println("共同的孤立节点: " + common.size());
        System.out.println("只在方法1中的孤立节点: " + onlyInMethod1.size());
        System.out.println("只在方法2中的孤立节点: " + onlyInMethod2.size());
        
        if (!onlyInMethod1.isEmpty()) {
            System.out.println("\n🔸 只在方法1中检测到的孤立节点:");
            for (String nodeId : onlyInMethod1) {
                JsonNode node = rootNode.get(nodeId);
                String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
                int nodeType = node.has("nodeType") ? node.get("nodeType").asInt() : -1;
                System.out.println("   - " + nodeId + " (" + getNodeTypeName(nodeType) + "): " + nodeName);
            }
        }
        
        if (!onlyInMethod2.isEmpty()) {
            System.out.println("\n🔹 只在方法2中检测到的孤立节点:");
            for (String nodeId : onlyInMethod2) {
                JsonNode node = rootNode.get(nodeId);
                String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
                int nodeType = node.has("nodeType") ? node.get("nodeType").asInt() : -1;
                System.out.println("   - " + nodeId + " (" + getNodeTypeName(nodeType) + "): " + nodeName);
            }
        }
        
        // 分析连接关系的差异
        analyzeConnectionDifferences(rootNode);
    }
    
    /**
     * 方法1：NodeConnectionAnalyzer 的逻辑
     */
    private Set<String> analyzeMethod1(JsonNode rootNode) {
        Map<String, JsonNode> allNodes = new HashMap<>();
        Map<String, JsonNode> allConnections = new HashMap<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                allNodes.put(key, value);
            } else if (isConnectionObject(value)) {
                allConnections.put(key, value);
            }
        }
        
        System.out.println("   - 总节点数: " + allNodes.size());
        System.out.println("   - 总连线数: " + allConnections.size());
        
        Set<String> connectedNodes = new HashSet<>();
        
        // 找出所有有连接关系的节点
        for (JsonNode connection : allConnections.values()) {
            if (connection.has("source") && connection.get("source").has("id")) {
                String sourceId = connection.get("source").get("id").asText();
                if (allNodes.containsKey(sourceId)) {
                    connectedNodes.add(sourceId);
                }
            }
            
            if (connection.has("target") && connection.get("target").has("id")) {
                String targetId = connection.get("target").get("id").asText();
                if (allNodes.containsKey(targetId)) {
                    connectedNodes.add(targetId);
                }
            }
        }
        
        // 找出孤立节点
        Set<String> isolatedNodes = new HashSet<>();
        for (String nodeId : allNodes.keySet()) {
            if (!connectedNodes.contains(nodeId)) {
                isolatedNodes.add(nodeId);
            }
        }
        
        System.out.println("   - 已连接节点: " + connectedNodes.size());
        System.out.println("   - 孤立节点: " + isolatedNodes.size());
        
        return isolatedNodes;
    }
    
    /**
     * 方法2：FlowLayoutProcessor 的逻辑
     */
    private Set<String> analyzeMethod2(JsonNode rootNode) {
        Map<String, FlowNode> nodes = new HashMap<>();
        Map<String, List<String>> adjacencyList = new HashMap<>();
        Map<String, Integer> inDegree = new HashMap<>();
        
        // 第一遍：收集所有节点
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                FlowNode node = parseFlowNode(key, value);
                nodes.put(key, node);
                adjacencyList.put(key, new ArrayList<>());
                inDegree.put(key, 0);
            }
        }
        
        // 第二遍：收集所有连线并构建邻接表
        fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isConnectionObject(value)) {
                FlowConnection connection = parseFlowConnection(key, value);
                if (connection != null && connection.isValidConnection()) {
                    String sourceId = connection.getSourceId();
                    String targetId = connection.getTargetId();
                    
                    // 排除指向recall节点的连线（避免循环）
                    if (!"recall".equals(targetId) && nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                        adjacencyList.get(sourceId).add(targetId);
                        inDegree.put(targetId, inDegree.get(targetId) + 1);
                    }
                }
            }
        }
        
        System.out.println("   - 总节点数: " + nodes.size());
        System.out.println("   - 有效连线数: " + adjacencyList.values().stream().mapToInt(List::size).sum());
        
        // 模拟层级计算逻辑
        for (FlowNode node : nodes.values()) {
            node.setLevel(0);
        }
        
        // 找到所有开始节点
        Queue<String> queue = new LinkedList<>();
        Set<String> processed = new HashSet<>();
        
        for (Map.Entry<String, FlowNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            FlowNode node = entry.getValue();
            
            if (node.isStartNode() || inDegree.get(nodeId) == 0) {
                node.setLevel(1);
                queue.offer(nodeId);
                processed.add(nodeId);
            }
        }
        
        // 特殊处理：recall和byStart节点
        if (nodes.containsKey("recall")) {
            nodes.get("recall").setLevel(999);
            nodes.get("recall").setSpecialNode(true);
        }
        if (nodes.containsKey("byStart")) {
            nodes.get("byStart").setLevel(999);
            nodes.get("byStart").setSpecialNode(true);
        }
        
        // BFS层级传播（简化版）
        int maxIterations = nodes.size() * 2;
        int iteration = 0;
        
        while (!queue.isEmpty() && iteration < maxIterations) {
            iteration++;
            int queueSize = queue.size();
            
            for (int i = 0; i < queueSize; i++) {
                String currentNodeId = queue.poll();
                FlowNode currentNode = nodes.get(currentNodeId);
                int currentLevel = currentNode.getLevel();
                
                List<String> children = adjacencyList.get(currentNodeId);
                if (children != null) {
                    for (String childId : children) {
                        if (nodes.containsKey(childId) && !processed.contains(childId)) {
                            FlowNode childNode = nodes.get(childId);
                            
                            if (childNode.isSpecialNode()) {
                                continue;
                            }
                            
                            int newLevel = currentLevel + 1;
                            if (childNode.getLevel() == 0 || newLevel > childNode.getLevel()) {
                                childNode.setLevel(newLevel);
                            }
                            
                            if (!processed.contains(childId)) {
                                queue.offer(childId);
                                processed.add(childId);
                            }
                        }
                    }
                }
            }
        }
        
        // 找出孤立节点
        Set<String> isolatedNodes = new HashSet<>();
        for (Map.Entry<String, FlowNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            FlowNode node = entry.getValue();
            
            if (node.getLevel() == 0 && !node.isSpecialNode()) {
                isolatedNodes.add(nodeId);
            }
        }
        
        System.out.println("   - 已处理节点: " + processed.size());
        System.out.println("   - 孤立节点: " + isolatedNodes.size());
        
        return isolatedNodes;
    }
    
    private void analyzeConnectionDifferences(JsonNode rootNode) {
        System.out.println("\n🔗 连接关系详细分析:");
        
        int totalConnections = 0;
        int validConnections = 0;
        int recallConnections = 0;
        int invalidConnections = 0;
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isConnectionObject(value)) {
                totalConnections++;
                
                String sourceId = null;
                String targetId = null;
                
                if (value.has("source") && value.get("source").has("id")) {
                    sourceId = value.get("source").get("id").asText();
                }
                
                if (value.has("target") && value.get("target").has("id")) {
                    targetId = value.get("target").get("id").asText();
                }
                
                if (sourceId != null && targetId != null) {
                    if ("recall".equals(targetId)) {
                        recallConnections++;
                    } else {
                        validConnections++;
                    }
                } else {
                    invalidConnections++;
                }
            }
        }
        
        System.out.println("   - 总连线对象: " + totalConnections);
        System.out.println("   - 有效连线: " + validConnections);
        System.out.println("   - 指向recall的连线: " + recallConnections);
        System.out.println("   - 无效连线: " + invalidConnections);
        
        System.out.println("\n💡 差异原因分析:");
        System.out.println("   - 方法2排除了指向recall节点的连线");
        System.out.println("   - 方法2使用了更复杂的层级计算逻辑");
        System.out.println("   - 方法2对特殊节点有特殊处理");
    }
    
    // 辅助方法
    private boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private FlowNode parseFlowNode(String id, JsonNode nodeJson) {
        FlowNode node = new FlowNode();
        node.setId(id);
        
        if (nodeJson.has("nodeName")) {
            node.setNodeName(nodeJson.get("nodeName").asText());
        }
        
        if (nodeJson.has("nodeType")) {
            node.setNodeType(nodeJson.get("nodeType").asInt());
        }
        
        return node;
    }
    
    private FlowConnection parseFlowConnection(String id, JsonNode connectionJson) {
        FlowConnection connection = new FlowConnection();
        connection.setId(id);
        
        if (connectionJson.has("source")) {
            JsonNode sourceNode = connectionJson.get("source");
            FlowConnection.NodeReference source = new FlowConnection.NodeReference();
            
            if (sourceNode.has("id")) {
                source.setId(sourceNode.get("id").asText());
            }
            connection.setSource(source);
        }
        
        if (connectionJson.has("target")) {
            JsonNode targetNode = connectionJson.get("target");
            FlowConnection.NodeReference target = new FlowConnection.NodeReference();
            
            if (targetNode.has("id")) {
                target.setId(targetNode.get("id").asText());
            }
            connection.setTarget(target);
        }
        
        return connection;
    }
    
    private String getNodeTypeName(int nodeType) {
        switch (nodeType) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            case 99: return "连线";
            default: return "未知类型(" + nodeType + ")";
        }
    }
}
