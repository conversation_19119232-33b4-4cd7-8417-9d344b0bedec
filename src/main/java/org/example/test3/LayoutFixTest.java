package org.example.test3;

/**
 * 布局修复测试程序
 * 测试网关节点独占层级和结束节点位置修复
 */
public class LayoutFixTest {
    public static void main(String[] args) {
        System.out.println("🔧 布局修复测试程序");
        System.out.println("=".repeat(60));
        System.out.println("测试目标：");
        System.out.println("1. 网关节点独占层级实现");
        System.out.println("2. 结束节点位置修复");
        System.out.println("3. 层级计算逻辑验证");
        System.out.println("4. 坐标计算优化");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_fixed.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行修复后的布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 修复后的布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 修复验证要点：");
                System.out.println("1. 检查网关节点是否独占层级行");
                System.out.println("2. 检查结束节点是否位于流程末端");
                System.out.println("3. 检查坐标计算是否遵循SQL公式");
                System.out.println("4. 检查层级内排序是否正确");
                
            } else {
                System.err.println("❌ 修复后的布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 布局修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
