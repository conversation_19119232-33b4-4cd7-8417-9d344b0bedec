package org.example.test3;

/**
 * 布局间距调整测试程序
 * 测试recall节点位置和节点间距减半的效果
 */
public class LayoutSpacingTest {
    public static void main(String[] args) {
        System.out.println("📐 布局间距调整测试程序");
        System.out.println("=".repeat(80));
        System.out.println("调整内容：");
        System.out.println("1. recall节点放到byStart节点左侧2000px");
        System.out.println("2. 节点横向间隔减半：500px -> 250px");
        System.out.println("3. 节点纵向间隔减半：400px -> 200px");
        System.out.println("4. 网关节点居中位置相应调整");
        System.out.println();
        
        System.out.println("预期效果：");
        System.out.println("✅ 流程图更加紧凑，节点密度增加");
        System.out.println("✅ recall节点在byStart节点左侧，便于回退流程");
        System.out.println("✅ 整体布局更适合较小的显示区域");
        System.out.println("✅ 保持流程逻辑清晰的同时节省空间");
        System.out.println();
        
        System.out.println("坐标计算公式调整：");
        System.out.println("修改前：");
        System.out.println("  - X坐标: 150 + (levelOrder-1) * 500");
        System.out.println("  - Y坐标: 150 + (level-1) * 400");
        System.out.println("  - 网关居中: 150 + 2 * 500 = 1150");
        System.out.println();
        System.out.println("修改后：");
        System.out.println("  - X坐标: 150 + (levelOrder-1) * 250");
        System.out.println("  - Y坐标: 150 + (level-1) * 200");
        System.out.println("  - 网关居中: 150 + 2 * 250 = 650");
        System.out.println("  - recall节点: byStartX - 2000");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_compact.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行紧凑布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 紧凑布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ recall节点X坐标 = byStart节点X坐标 - 2000");
                System.out.println("2. ✅ 普通节点间距减半，布局更紧凑");
                System.out.println("3. ✅ 网关节点居中位置正确调整");
                System.out.println("4. ✅ 整体流程逻辑保持清晰");
                
                System.out.println();
                System.out.println("📋 预期坐标示例：");
                System.out.println("Level 1, Order 1: X=150, Y=150");
                System.out.println("Level 1, Order 2: X=400, Y=150 (间距250)");
                System.out.println("Level 2, Order 1: X=150, Y=350 (间距200)");
                System.out.println("网关节点居中: X=650");
                System.out.println("recall节点: X=byStartX-2000");
                
                System.out.println();
                System.out.println("🔍 调试信息验证：");
                System.out.println("- 检查控制台输出中的'处理特殊节点坐标'");
                System.out.println("- 确认'更新recall节点 XXX 坐标'消息");
                System.out.println("- 验证byStart节点坐标和recall节点的相对位置");
                
                System.out.println();
                System.out.println("📐 布局优势：");
                System.out.println("✅ 空间利用率提高50%");
                System.out.println("✅ 适合较小屏幕显示");
                System.out.println("✅ recall节点位置更符合用户习惯");
                System.out.println("✅ 保持专业的流程图外观");
                
            } else {
                System.err.println("❌ 紧凑布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 布局间距调整测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. byStart节点不存在或坐标异常");
            System.err.println("   2. recall节点识别逻辑错误");
            System.err.println("   3. 坐标计算公式错误");
            System.err.println("   4. 几何信息更新失败");
        }
    }
}
