package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 连线处理调试器
 * 检查连线识别和处理逻辑
 */
public class ConnectionDebugger {
    
    public static void main(String[] args) {
        System.out.println("🔍 连线处理调试器");
        System.out.println("=".repeat(60));
        
        try {
            debugConnectionProcessing("src/main/java/org/example/test3/processDesignModel.json");
        } catch (Exception e) {
            System.err.println("❌ 调试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void debugConnectionProcessing(String filePath) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(filePath));
        
        System.out.println("📊 JSON文件结构分析:");
        
        int totalObjects = 0;
        int nodeObjects = 0;
        int connectionObjects = 0;
        int edgeObjects = 0;
        
        List<String> connectionKeys = new ArrayList<>();
        List<String> nodeKeys = new ArrayList<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            totalObjects++;
            
            // 检查是否为节点对象
            if (isNodeObject(value)) {
                nodeObjects++;
                nodeKeys.add(key);
            }
            
            // 检查是否为连线对象（多种判断方式）
            if (isConnectionObject(value)) {
                connectionObjects++;
                connectionKeys.add(key);
            }
            
            // 检查是否有edge标记
            if (value.has("edge") && "true".equals(value.get("edge").asText())) {
                edgeObjects++;
            }
        }
        
        System.out.println("   - 总对象数: " + totalObjects);
        System.out.println("   - 节点对象: " + nodeObjects);
        System.out.println("   - 连线对象: " + connectionObjects);
        System.out.println("   - edge标记对象: " + edgeObjects);
        
        // 输出连线对象详情
        System.out.println("\n🔗 连线对象详情:");
        if (connectionKeys.isEmpty()) {
            System.out.println("   ❌ 未发现连线对象！");
            
            // 尝试查找可能的连线对象
            System.out.println("\n🔍 查找可能的连线对象:");
            fields = rootNode.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (value.has("source") || value.has("target") || 
                    value.has("edge") || key.contains("edge") || key.contains("connection")) {
                    
                    System.out.println("   可能的连线: " + key);
                    System.out.println("     字段: " + getFieldNames(value));
                    
                    if (value.has("source")) {
                        System.out.println("     source: " + value.get("source"));
                    }
                    if (value.has("target")) {
                        System.out.println("     target: " + value.get("target"));
                    }
                }
            }
        } else {
            for (String key : connectionKeys) {
                JsonNode conn = rootNode.get(key);
                System.out.println("   连线: " + key);
                
                if (conn.has("source")) {
                    JsonNode source = conn.get("source");
                    String sourceId = source.has("id") ? source.get("id").asText() : "无ID";
                    System.out.println("     source: " + sourceId);
                }
                
                if (conn.has("target")) {
                    JsonNode target = conn.get("target");
                    String targetId = target.has("id") ? target.get("id").asText() : "无ID";
                    System.out.println("     target: " + targetId);
                }
                
                System.out.println("     所有字段: " + getFieldNames(conn));
            }
        }
        
        // 检查节点几何信息
        System.out.println("\n📐 节点几何信息检查:");
        int nodesWithGeometry = 0;
        for (String key : nodeKeys) {
            JsonNode node = rootNode.get(key);
            if (node.has("geometry")) {
                nodesWithGeometry++;
                JsonNode geometry = node.get("geometry");
                
                if (nodeKeys.indexOf(key) < 3) { // 只显示前3个
                    System.out.println("   节点 " + key + ":");
                    System.out.println("     x: " + (geometry.has("x") ? geometry.get("x").asDouble() : "无"));
                    System.out.println("     y: " + (geometry.has("y") ? geometry.get("y").asDouble() : "无"));
                    System.out.println("     width: " + (geometry.has("width") ? geometry.get("width").asDouble() : "无"));
                    System.out.println("     height: " + (geometry.has("height") ? geometry.get("height").asDouble() : "无"));
                }
            }
        }
        System.out.println("   包含几何信息的节点: " + nodesWithGeometry + "/" + nodeObjects);
        
        // 模拟FlowLayoutProcessor的连线识别逻辑
        System.out.println("\n🧪 模拟FlowLayoutProcessor连线识别:");
        testFlowLayoutProcessorLogic(rootNode);
    }
    
    private static void testFlowLayoutProcessorLogic(JsonNode rootNode) {
        // 模拟FlowLayoutProcessor中的连线识别逻辑
        Map<String, FlowConnection> connections = new HashMap<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            // 使用FlowLayoutProcessor的判断逻辑
            if (isConnectionByFlowProcessor(value)) {
                try {
                    FlowConnection connection = parseFlowConnection(key, value);
                    if (connection != null && connection.isValidConnection()) {
                        connections.put(key, connection);
                    }
                } catch (Exception e) {
                    System.out.println("   解析连线失败: " + key + " - " + e.getMessage());
                }
            }
        }
        
        System.out.println("   FlowLayoutProcessor识别的连线数: " + connections.size());
        
        for (Map.Entry<String, FlowConnection> entry : connections.entrySet()) {
            FlowConnection conn = entry.getValue();
            System.out.println("   " + entry.getKey() + ": " + conn.getSourceId() + " -> " + conn.getTargetId());
        }
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private static boolean isConnectionByFlowProcessor(JsonNode value) {
        // 复制FlowLayoutProcessor中的判断逻辑
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private static FlowConnection parseFlowConnection(String key, JsonNode value) {
        FlowConnection connection = new FlowConnection();
        connection.setId(key);
        
        if (value.has("source")) {
            JsonNode sourceNode = value.get("source");
            FlowConnection.NodeReference source = new FlowConnection.NodeReference();
            if (sourceNode.has("id")) {
                source.setId(sourceNode.get("id").asText());
            }
            connection.setSource(source);
        }
        
        if (value.has("target")) {
            JsonNode targetNode = value.get("target");
            FlowConnection.NodeReference target = new FlowConnection.NodeReference();
            if (targetNode.has("id")) {
                target.setId(targetNode.get("id").asText());
            }
            connection.setTarget(target);
        }
        
        return connection;
    }
    
    private static String getFieldNames(JsonNode node) {
        List<String> fields = new ArrayList<>();
        node.fieldNames().forEachRemaining(fields::add);
        return String.join(", ", fields);
    }
}
