package org.example.test3;

/**
 * 孤立节点修复测试程序
 * 测试孤立节点层级分配和坐标计算的修复效果
 */
public class IsolatedNodeFixTest {
    public static void main(String[] args) {
        System.out.println("🏝️ 孤立节点修复测试程序");
        System.out.println("=".repeat(80));
        System.out.println("修复目标：");
        System.out.println("1. 孤立节点不再分配到特殊层级999");
        System.out.println("2. 孤立节点在主流程下方连续排列");
        System.out.println("3. 孤立节点使用标准坐标计算公式");
        System.out.println("4. 整体布局保持连续性和美观性");
        System.out.println();
        
        System.out.println("修复前问题：");
        System.out.println("❌ 孤立节点被分配到level=999，显示在右侧");
        System.out.println("❌ 破坏了流程图的连续性");
        System.out.println("❌ 孤立节点与主流程在视觉上分离");
        System.out.println();
        
        System.out.println("修复后效果：");
        System.out.println("✅ 孤立节点分配到连续层级（maxLevel+1, maxLevel+2...）");
        System.out.println("✅ 孤立节点在主流程下方垂直排列");
        System.out.println("✅ 使用标准坐标公式：x=150+(order-1)*500, y=150+(level-1)*400");
        System.out.println("✅ 整体布局紧凑美观");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_isolated_fix.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行孤立节点修复后的布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 孤立节点修复后的布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 修复验证要点：");
                System.out.println("1. ✅ 孤立节点层级连续性：检查是否从maxLevel+1开始连续分配");
                System.out.println("2. ✅ 坐标计算一致性：孤立节点是否使用标准公式");
                System.out.println("3. ✅ 布局连续性：孤立节点是否在主流程下方排列");
                System.out.println("4. ✅ 视觉效果：整体布局是否紧凑美观");
                
                System.out.println();
                System.out.println("📋 预期布局结构：");
                System.out.println("Level 1: 开始节点");
                System.out.println("Level 2: 初始化节点");
                System.out.println("Level 3: 主流程节点...");
                System.out.println("Level N: 主流程结束节点");
                System.out.println("Level N+1: 第一个孤立节点");
                System.out.println("Level N+2: 第二个孤立节点");
                System.out.println("Level N+3: 第三个孤立节点...");
                
                System.out.println();
                System.out.println("📐 坐标计算验证：");
                System.out.println("- 主流程节点：标准公式");
                System.out.println("- 孤立节点：相同的标准公式");
                System.out.println("- 网关节点：标准公式 + 50px垂直偏移");
                System.out.println("- recall节点：特殊坐标处理");
                
            } else {
                System.err.println("❌ 孤立节点修复后的布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 孤立节点修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 孤立节点识别逻辑错误");
            System.err.println("   2. 层级分配算法异常");
            System.err.println("   3. 坐标计算公式错误");
            System.err.println("   4. levelOrder计算问题");
        }
    }
}
