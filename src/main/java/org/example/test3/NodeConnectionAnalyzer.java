package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 节点连接关系分析器
 * 专门用于分析processDesignModel.json中的孤立节点问题
 */
public class NodeConnectionAnalyzer {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    
    public static void main(String[] args) {
        System.out.println("🔍 节点连接关系分析器");
        System.out.println("=".repeat(60));
        
        try {
            NodeConnectionAnalyzer analyzer = new NodeConnectionAnalyzer();
            analyzer.analyzeConnections();
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void analyzeConnections() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(INPUT_FILE));
        
        // 收集所有节点
        Map<String, JsonNode> allNodes = new HashMap<>();
        Map<String, JsonNode> allConnections = new HashMap<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                allNodes.put(key, value);
            } else if (isConnectionObject(value)) {
                allConnections.put(key, value);
            }
        }
        
        System.out.println("📊 基本统计:");
        System.out.println("   - 总节点数: " + allNodes.size());
        System.out.println("   - 总连线数: " + allConnections.size());
        
        // 分析节点类型分布
        analyzeNodeTypes(allNodes);
        
        // 分析连接关系
        analyzeConnectionRelationships(allNodes, allConnections);
        
        // 查找孤立节点
        findIsolatedNodes(allNodes, allConnections);
        
        // 分析特殊节点
        analyzeSpecialNodes(allNodes, allConnections);
    }
    
    private void analyzeNodeTypes(Map<String, JsonNode> nodes) {
        System.out.println("\n📋 节点类型分布:");
        
        Map<Integer, List<String>> typeGroups = new HashMap<>();
        
        for (Map.Entry<String, JsonNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            JsonNode node = entry.getValue();
            
            if (node.has("nodeType")) {
                int nodeType = node.get("nodeType").asInt();
                typeGroups.computeIfAbsent(nodeType, k -> new ArrayList<>()).add(nodeId);
            }
        }
        
        typeGroups.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int type = entry.getKey();
                List<String> nodeIds = entry.getValue();
                String typeName = getNodeTypeName(type);
                System.out.println(String.format("   - %s (类型%d): %d个", typeName, type, nodeIds.size()));
                
                // 显示前5个节点ID
                if (nodeIds.size() <= 5) {
                    System.out.println("     节点: " + String.join(", ", nodeIds));
                } else {
                    List<String> firstFive = nodeIds.subList(0, 5);
                    System.out.println("     节点: " + String.join(", ", firstFive) + "... (共" + nodeIds.size() + "个)");
                }
            });
    }
    
    private void analyzeConnectionRelationships(Map<String, JsonNode> nodes, Map<String, JsonNode> connections) {
        System.out.println("\n🔗 连接关系分析:");
        
        Set<String> connectedNodes = new HashSet<>();
        Map<String, List<String>> incomingConnections = new HashMap<>();
        Map<String, List<String>> outgoingConnections = new HashMap<>();
        
        int validConnections = 0;
        int invalidConnections = 0;
        
        for (Map.Entry<String, JsonNode> entry : connections.entrySet()) {
            String connectionId = entry.getKey();
            JsonNode connection = entry.getValue();
            
            String sourceId = null;
            String targetId = null;
            
            if (connection.has("source") && connection.get("source").has("id")) {
                sourceId = connection.get("source").get("id").asText();
            }
            
            if (connection.has("target") && connection.get("target").has("id")) {
                targetId = connection.get("target").get("id").asText();
            }
            
            if (sourceId != null && targetId != null && 
                nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                
                validConnections++;
                connectedNodes.add(sourceId);
                connectedNodes.add(targetId);
                
                outgoingConnections.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
                incomingConnections.computeIfAbsent(targetId, k -> new ArrayList<>()).add(sourceId);
            } else {
                invalidConnections++;
                System.out.println("   ⚠️ 无效连线: " + connectionId + " (" + sourceId + " -> " + targetId + ")");
            }
        }
        
        System.out.println("   - 有效连线: " + validConnections);
        System.out.println("   - 无效连线: " + invalidConnections);
        System.out.println("   - 已连接节点: " + connectedNodes.size());
        System.out.println("   - 孤立节点: " + (nodes.size() - connectedNodes.size()));
    }
    
    private void findIsolatedNodes(Map<String, JsonNode> nodes, Map<String, JsonNode> connections) {
        System.out.println("\n🏝️ 孤立节点详细分析:");
        
        Set<String> connectedNodes = new HashSet<>();
        
        // 找出所有有连接关系的节点
        for (JsonNode connection : connections.values()) {
            if (connection.has("source") && connection.get("source").has("id")) {
                String sourceId = connection.get("source").get("id").asText();
                if (nodes.containsKey(sourceId)) {
                    connectedNodes.add(sourceId);
                }
            }
            
            if (connection.has("target") && connection.get("target").has("id")) {
                String targetId = connection.get("target").get("id").asText();
                if (nodes.containsKey(targetId)) {
                    connectedNodes.add(targetId);
                }
            }
        }
        
        // 找出孤立节点
        List<String> isolatedNodes = new ArrayList<>();
        for (String nodeId : nodes.keySet()) {
            if (!connectedNodes.contains(nodeId)) {
                isolatedNodes.add(nodeId);
            }
        }
        
        System.out.println("   孤立节点总数: " + isolatedNodes.size());
        
        if (!isolatedNodes.isEmpty()) {
            // 按节点类型分组显示孤立节点
            Map<Integer, List<String>> isolatedByType = new HashMap<>();
            
            for (String nodeId : isolatedNodes) {
                JsonNode node = nodes.get(nodeId);
                int nodeType = node.has("nodeType") ? node.get("nodeType").asInt() : -1;
                isolatedByType.computeIfAbsent(nodeType, k -> new ArrayList<>()).add(nodeId);
            }
            
            isolatedByType.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    int type = entry.getKey();
                    List<String> nodeIds = entry.getValue();
                    String typeName = getNodeTypeName(type);
                    
                    System.out.println(String.format("   - %s: %d个", typeName, nodeIds.size()));
                    
                    // 显示所有孤立节点（如果不超过10个）
                    if (nodeIds.size() <= 10) {
                        for (String nodeId : nodeIds) {
                            JsonNode node = nodes.get(nodeId);
                            String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
                            System.out.println("     * " + nodeId + " (" + nodeName + ")");
                        }
                    } else {
                        for (int i = 0; i < 10; i++) {
                            String nodeId = nodeIds.get(i);
                            JsonNode node = nodes.get(nodeId);
                            String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
                            System.out.println("     * " + nodeId + " (" + nodeName + ")");
                        }
                        System.out.println("     ... 还有 " + (nodeIds.size() - 10) + " 个节点");
                    }
                });
        }
    }
    
    private void analyzeSpecialNodes(Map<String, JsonNode> nodes, Map<String, JsonNode> connections) {
        System.out.println("\n⚡ 特殊节点分析:");
        
        // 检查特殊节点
        String[] specialNodeIds = {"start", "init", "recall", "byStart"};
        
        for (String specialId : specialNodeIds) {
            if (nodes.containsKey(specialId)) {
                JsonNode node = nodes.get(specialId);
                int nodeType = node.has("nodeType") ? node.get("nodeType").asInt() : -1;
                String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
                
                System.out.println(String.format("   - %s (类型%d): %s", specialId, nodeType, nodeName));
                
                // 检查该节点的连接情况
                int incomingCount = 0;
                int outgoingCount = 0;
                
                for (JsonNode connection : connections.values()) {
                    if (connection.has("source") && connection.get("source").has("id") &&
                        specialId.equals(connection.get("source").get("id").asText())) {
                        outgoingCount++;
                    }
                    
                    if (connection.has("target") && connection.get("target").has("id") &&
                        specialId.equals(connection.get("target").get("id").asText())) {
                        incomingCount++;
                    }
                }
                
                System.out.println(String.format("     入度: %d, 出度: %d", incomingCount, outgoingCount));
            } else {
                System.out.println("   - " + specialId + ": 不存在");
            }
        }
    }
    
    private boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private String getNodeTypeName(int nodeType) {
        switch (nodeType) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            case 99: return "连线";
            default: return "未知类型(" + nodeType + ")";
        }
    }
}
