{"0": {"id": "0", "parent": null, "source": null, "target": null, "children": null, "mxObjectId": "mxCell#41"}, "1": {"id": "1", "parent": null, "source": null, "target": null, "children": null, "mxObjectId": "mxCell#40"}, "2": {"id": "2", "edge": "true", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3;exitX=0.5;exitY=1;entryX=0.5;entryY=0;jettySize=auto;orthogonalLoop=1;", "value": "null", "parent": null, "source": {"id": "ddbe7654-984f-4422-1543-cb60a9ed2ddd", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-填报合同信息-上传合同文件-发起合同_fromEBPM_ddbe7654-984f-4422-1543-cb60a9ed2ddd</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 1036, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "服务-填报合同信息-上传合同文件-发起合同_fromEBPM_ddbe7654-984f-4422-1543-cb60a9ed2ddd", "nodeType": 9, "mxObjectId": "mxCell#90", "connectable": "true"}, "target": {"id": "56c3118a-30c3-d22e-3d13-ca03cb41067b", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务12_56c3118a-30c3-d22e-3d13-ca03cb41067b</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1127, "y": 1296, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "服务12_56c3118a-30c3-d22e-3d13-ca03cb41067b", "nodeType": 9, "mxObjectId": "mxCell#64", "connectable": "true"}, "geometry": {"x": 0, "y": 0, "width": 0, "height": 0, "relative": 1, "abspoints": [{"x": 1536, "y": 1146}, {"x": 1536, "y": 1221}, {"x": 1617, "y": 1221}, {"x": 1617, "y": 1296}]}, "nodeType": 99, "mxObjectId": "mxCell#42"}, "_renderInfo": {"type": "mxgraph-optimized", "nodeCount": 0, "layoutAlgorithm": "hierarchical-bfs", "generatedAt": 1753766179464}}