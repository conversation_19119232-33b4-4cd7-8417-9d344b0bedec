package org.example.test3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 流程连线实体类
 * 用于表示流程图中节点之间的连接关系
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowConnection {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("source")
    private NodeReference source;
    
    @JsonProperty("target")
    private NodeReference target;
    
    @JsonProperty("value")
    private String value;
    
    @JsonProperty("nodeType")
    private Integer nodeType;

    @JsonProperty("geometry")
    private Geometry geometry;

    // mxGraph格式的几何信息（用于智能连线布局）
    private MxGraphConnectionGeometry mxGraphGeometry;

    public FlowConnection() {}
    
    public FlowConnection(String id, NodeReference source, NodeReference target) {
        this.id = id;
        this.source = source;
        this.target = target;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public NodeReference getSource() {
        return source;
    }
    
    public void setSource(NodeReference source) {
        this.source = source;
    }
    
    public NodeReference getTarget() {
        return target;
    }
    
    public void setTarget(NodeReference target) {
        this.target = target;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public Integer getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }

    public Geometry getGeometry() {
        return geometry;
    }

    public void setGeometry(Geometry geometry) {
        this.geometry = geometry;
    }

    public MxGraphConnectionGeometry getMxGraphGeometry() {
        return mxGraphGeometry;
    }

    public void setMxGraphGeometry(MxGraphConnectionGeometry mxGraphGeometry) {
        this.mxGraphGeometry = mxGraphGeometry;
    }
    
    /**
     * 判断是否为有效连线（有source和target）
     */
    public boolean isValidConnection() {
        return source != null && target != null && 
               source.getId() != null && target.getId() != null;
    }
    
    /**
     * 判断是否为重新提交连线（需要跳过的连线）
     */
    public boolean isRecallConnection() {
        return target != null && "recall".equals(target.getId());
    }
    
    /**
     * 获取源节点ID
     */
    public String getSourceId() {
        return source != null ? source.getId() : null;
    }
    
    /**
     * 获取目标节点ID
     */
    public String getTargetId() {
        return target != null ? target.getId() : null;
    }
    
    @Override
    public String toString() {
        return String.format("FlowConnection{id='%s', source='%s', target='%s', value='%s'}", 
                id, getSourceId(), getTargetId(), value);
    }
    
    /**
     * 节点引用内部类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NodeReference {
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("nodeName")
        private String nodeName;
        
        @JsonProperty("nodeType")
        private Integer nodeType;
        
        public NodeReference() {}
        
        public NodeReference(String id) {
            this.id = id;
        }
        
        public NodeReference(String id, String nodeName, Integer nodeType) {
            this.id = id;
            this.nodeName = nodeName;
            this.nodeType = nodeType;
        }
        
        // Getter和Setter方法
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getNodeName() {
            return nodeName;
        }
        
        public void setNodeName(String nodeName) {
            this.nodeName = nodeName;
        }
        
        public Integer getNodeType() {
            return nodeType;
        }
        
        public void setNodeType(Integer nodeType) {
            this.nodeType = nodeType;
        }
        
        @Override
        public String toString() {
            return String.format("NodeReference{id='%s', nodeName='%s', nodeType=%d}", 
                    id, nodeName, nodeType);
        }
    }

    /**
     * 连线几何信息内部类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Geometry {
        @JsonProperty("startX")
        private double startX;

        @JsonProperty("startY")
        private double startY;

        @JsonProperty("endX")
        private double endX;

        @JsonProperty("endY")
        private double endY;

        @JsonProperty("controlPoints")
        private java.util.List<ControlPoint> controlPoints;

        @JsonProperty("pathType")
        private String pathType = "curved";

        public Geometry() {}

        // Getter和Setter方法
        public double getStartX() {
            return startX;
        }

        public void setStartX(double startX) {
            this.startX = startX;
        }

        public double getStartY() {
            return startY;
        }

        public void setStartY(double startY) {
            this.startY = startY;
        }

        public double getEndX() {
            return endX;
        }

        public void setEndX(double endX) {
            this.endX = endX;
        }

        public double getEndY() {
            return endY;
        }

        public void setEndY(double endY) {
            this.endY = endY;
        }

        public java.util.List<ControlPoint> getControlPoints() {
            return controlPoints;
        }


        // 便捷方法：从ConnectionPoint列表设置控制点
        public void setControlPointsFromPath(java.util.List<org.example.test3.FlowLayoutProcessor.ConnectionPoint> connectionPoints) {
            if (connectionPoints != null && !connectionPoints.isEmpty()) {
                this.controlPoints = new java.util.ArrayList<>();
                for (org.example.test3.FlowLayoutProcessor.ConnectionPoint cp : connectionPoints) {
                    ControlPoint point = new ControlPoint();
                    point.setX(cp.x);
                    point.setY(cp.y);
                    this.controlPoints.add(point);
                }
            }
        }

        public String getPathType() {
            return pathType;
        }

        public void setPathType(String pathType) {
            this.pathType = pathType;
        }

        @Override
        public String toString() {
            return String.format("Geometry{start=(%.1f,%.1f), end=(%.1f,%.1f), controlPoints=%d, pathType='%s'}",
                    startX, startY, endX, endY,
                    controlPoints != null ? controlPoints.size() : 0, pathType);
        }
    }

    /**
     * 控制点内部类
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ControlPoint {
        @JsonProperty("x")
        private double x;

        @JsonProperty("y")
        private double y;

        public ControlPoint() {}

        public ControlPoint(double x, double y) {
            this.x = x;
            this.y = y;
        }

        public double getX() {
            return x;
        }

        public void setX(double x) {
            this.x = x;
        }

        public double getY() {
            return y;
        }

        public void setY(double y) {
            this.y = y;
        }

        @Override
        public String toString() {
            return String.format("(%.1f,%.1f)", x, y);
        }
    }
}
