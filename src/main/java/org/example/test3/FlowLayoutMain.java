package org.example.test3;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 基于SQL算法的Mxgraph流程图自动布局主程序
 *
 * 🎯 核心功能：
 * 1. 完全基于ProcessModelHierarchicalLayout_Fixed.sql算法实现
 * 2. 解析processDesignModel.json文件中的流程节点和连线信息
 * 3. 使用SQL精确的坐标计算公式，确保布局一致性
 * 4. 生成专为Mxgraph前端渲染优化的JSON文件
 *
 * 🔄 SQL算法移植步骤：
 * - 第一步：解析JSON结构并构建节点关系图
 * - 第二步：使用循环迭代计算节点层级（避免递归CTE）
 * - 第三步：层级内按节点ID排序（与SQL一致）
 * - 第四步：使用SQL精确坐标公式计算节点位置
 * - 第五步：生成Mxgraph优化的输出文件
 *
 * ⚡ SQL算法特性：
 * - 精确的坐标计算：X=150+(order-1)*500, Y=150+(level-1)*400
 * - 层级内按节点ID排序（ROW_NUMBER() OVER ORDER BY id）
 * - 特殊节点（Recall、byStart）独立处理
 * - 移除所有边数据，专注节点坐标处理
 * - 输出JSON格式完全兼容Mxgraph前端渲染
 *
 * 📐 SQL精确布局算法：
 * - 基础偏移：150px
 * - 水平间距：500px（level_order间距）
 * - 垂直间距：400px（level_num间距）
 * - 网格状布局，避免节点重叠
 */
public class FlowLayoutMain {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    private static final String OUTPUT_FILE = "src/main/java/org/example/test3/processDesignModel_updated.json";
    
    public static void main(String[] args) {
        System.out.println("🎯 基于SQL算法的Mxgraph流程图自动布局处理程序");
        System.out.println("开始时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();
        
        try {
            // 验证输入文件是否存在
            File inputFile = new File(INPUT_FILE);
            if (!inputFile.exists()) {
                System.err.println("错误：输入文件不存在 - " + INPUT_FILE);
                System.err.println("请确保文件路径正确，并且文件存在。");
                return;
            }
            
            System.out.println("输入文件: " + INPUT_FILE);
            System.out.println("输出文件: " + OUTPUT_FILE);
            System.out.println("文件大小: " + formatFileSize(inputFile.length()));
            System.out.println();
            
            // 创建布局处理器并执行处理
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            processor.processFlowLayout(INPUT_FILE, OUTPUT_FILE);
            
            // 验证输出文件
            File outputFile = new File(OUTPUT_FILE);
            if (outputFile.exists()) {
                System.out.println();
                System.out.println("✅ Mxgraph优化处理成功完成！");
                System.out.println("📁 输出文件大小: " + formatFileSize(outputFile.length()));
                System.out.println("📍 输出文件位置: " + outputFile.getAbsolutePath());
                System.out.println("🎨 文件已优化用于Mxgraph前端渲染");
            } else {
                System.err.println("❌ 警告：输出文件未成功生成");
            }
            
        } catch (Exception e) {
            System.err.println("处理过程中发生错误:");
            System.err.println("错误类型: " + e.getClass().getSimpleName());
            System.err.println("错误信息: " + e.getMessage());
            e.printStackTrace();
        } finally {
            System.out.println();
            System.out.println("结束时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
    }
    
    /**
     * 格式化文件大小显示
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 显示使用说明
     */
    public static void showUsage() {
        System.out.println("流程图自动布局处理程序使用说明:");
        System.out.println();
        System.out.println("功能：");
        System.out.println("  - 自动分析流程图节点关系");
        System.out.println("  - 计算最优的节点布局坐标");
        System.out.println("  - 生成层级清晰的流程图布局");
        System.out.println("  - 避免节点重叠和混乱排列");
        System.out.println();
        System.out.println("输入文件要求：");
        System.out.println("  - JSON格式的流程图数据文件");
        System.out.println("  - 包含nodes数组（节点信息）");
        System.out.println("  - 包含lines数组（连线关系）");
        System.out.println("  - 每个节点包含id、nodeType、geometry等字段");
        System.out.println();
        System.out.println("输出结果：");
        System.out.println("  - 更新后的JSON文件（保持原始结构）");
        System.out.println("  - 重新计算的节点坐标");
        System.out.println("  - 详细的处理日志和统计报告");
        System.out.println();
        System.out.println("特殊处理：");
        System.out.println("  - 开始节点（nodeType=1）作为布局起点");
        System.out.println("  - 重新提交连线（target='recall'）被忽略");
        System.out.println("  - 特殊节点（recall、byStart）进行坐标偏移");
        System.out.println();
        System.out.println("布局算法：");
        System.out.println("  - 使用广度优先搜索(BFS)计算节点层级");
        System.out.println("  - X坐标 = 100 + (层级-1) × 300");
        System.out.println("  - Y坐标 = 单节点280，多节点200×序号");
        System.out.println("  - 特殊节点X坐标向左偏移1000px");
    }
}
