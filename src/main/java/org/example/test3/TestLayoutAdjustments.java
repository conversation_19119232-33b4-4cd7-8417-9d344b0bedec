package org.example.test3;

/**
 * 测试节点布局调整的效果
 * 验证recall节点和流程开始节点的坐标调整是否能实现直线连接
 */
public class TestLayoutAdjustments {
    
    public static void main(String[] args) {
        System.out.println("🧪 开始测试节点布局调整...");
        
        // 测试文件路径
        String inputFile = "src/main/resources/test_input.json";
        String outputFile = "src/main/resources/test_output_adjusted.json";
        
        try {
            // 创建布局处理器
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            
            // 处理布局
            processor.processFlowLayout(inputFile, outputFile);
            
            System.out.println("\n✅ 布局调整测试完成！");
            System.out.println("📄 输出文件: " + outputFile);
            System.out.println("\n📋 调整说明:");
            System.out.println("   1. recall节点的Y坐标已调整为与byStart节点相同");
            System.out.println("   2. 流程开始节点的X坐标已调整为与流程初始化节点相同");
            System.out.println("   3. 这些调整将使相关节点间的连线更加直线化");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
