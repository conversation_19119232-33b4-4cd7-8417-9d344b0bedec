请分析 `src/main/java/org/example/test3/processDesignModel.json` 文件，并实现一个流程图节点层级计算和坐标布局算法。具体要求如下：

**第一步：层级计算算法**
1. 解析 JSON 文件中的节点（nodes）和连线（edges）数据
2. 从开始节点（start node）开始进行广度优先遍历（BFS），根据连线的 `source` 和 `target` 字段建立有向图的节点关系
3. 层级分配规则：
   - 开始节点的 `level` 设置为 1
   - 普通节点：`level = 父节点的最大level + 1`
   - 网关节点（gateway）：`level = 父节点的最大level + 1`，且网关节点需要单独占据一行
4. 排除处理：在遍历过程中需要识别并排除"重新提交"类型的节点，防止形成死循环
5. 去重处理：对于有多个入口路径的节点，只保留来自最高 `level` 值路径的连接，删除其他重复的低level路径连接
6. 为每个节点计算两个属性：
   - `level_num`：节点所在的层级编号
   - `level_num_order`：节点在该层级内的排序编号（从1开始）

**第二步：层级统计和节点排序**
1. 按 `level_num` 对所有节点进行分组
2. 统计每个层级中的节点数量
3. 在每个层级内对节点进行排序（可按节点ID或其他逻辑排序），并分配 `level_num_order` 序号（从1开始递增）
4. 网关节点在其所在层级中单独处理，确保布局时独占一行

**第三步：坐标计算和布局算法**
1. 定义布局参数：
   - 起始坐标：以开始节点为基准点设定初始坐标 (startX, startY)
   - 节点间距：水平间距（horizontalSpacing）和垂直间距（verticalSpacing）
   - 节点尺寸：节点宽度（nodeWidth）和高度（nodeHeight）
2. 坐标计算规则：
   - Y坐标：`Y = startY + (level_num - 1) * verticalSpacing`
   - X坐标：`X = startX + (level_num_order - 1) * horizontalSpacing`
   - 对于网关节点：确保其在该层级中居中显示
3. 布局约束：
   - 节点按 `level_num` 从上往下排列
   - 同一 `level_num` 的节点按 `level_num_order` 从左到右排列
   - 网关节点单独占据一行，避免与其他节点重叠

**第四步：JSON文件更新**
1. 遍历计算出坐标的所有节点
2. 使用节点的唯一标识符（如 `target_id` 或 `id`）在原始 JSON 文件中定位对应的节点对象
3. 更新每个节点的 `x` 和 `y` 坐标属性
4. 保持 JSON 文件的其他数据结构和格式不变
5. 将更新后的数据写到 `processDesignModel_new.json` 文件

**实现要求：**
- 使用 Java 语言实现算法
- 确保代码具有良好的可读性和注释（使用简体中文注释）
- 处理边界情况（如孤立节点、循环引用等）
- 提供详细的日志输出，便于调试和验证结果
- 在修改文件前先备份原始数据

请先分析现有的 JSON 文件结构，然后实现完整的算法并修改 `processDesignModel.json` 文件中的坐标数据到新的请分析 `src/main/java/org/example/test3/processDesignModel.json` 文件，并实现一个流程图节点层级计算和坐标布局算法。具体要求如下：

**第一步：层级计算算法**
1. 解析 JSON 文件中的节点（nodes）和连线（edges）数据
2. 从开始节点（start node）开始进行广度优先遍历（BFS），根据连线的 `source` 和 `target` 字段建立有向图的节点关系
3. 层级分配规则：
   - 开始节点的 `level` 设置为 1
   - 普通节点：`level = 父节点的最大level + 1`
   - 网关节点（gateway）：`level = 父节点的最大level + 1`，且网关节点需要单独占据一行
4. 排除处理：在遍历过程中需要识别并排除"重新提交"类型的节点，防止形成死循环
5. 去重处理：对于有多个入口路径的节点，只保留来自最高 `level` 值路径的连接，删除其他重复的低level路径连接
6. 为每个节点计算两个属性：
   - `level_num`：节点所在的层级编号
   - `level_num_order`：节点在该层级内的排序编号（从1开始）

**第二步：层级统计和节点排序**
1. 按 `level_num` 对所有节点进行分组
2. 统计每个层级中的节点数量
3. 在每个层级内对节点进行排序（可按节点ID或其他逻辑排序），并分配 `level_num_order` 序号（从1开始递增）
4. 网关节点在其所在层级中单独处理，确保布局时独占一行

**第三步：坐标计算和布局算法**
1. 定义布局参数：
   - 起始坐标：以开始节点为基准点设定初始坐标 (startX, startY)
   - 节点间距：水平间距（horizontalSpacing）和垂直间距（verticalSpacing）
   - 节点尺寸：节点宽度（nodeWidth）和高度（nodeHeight）
2. 坐标计算规则：
   - Y坐标：`Y = startY + (level_num - 1) * verticalSpacing`
   - X坐标：`X = startX + (level_num_order - 1) * horizontalSpacing`
   - 对于网关节点：确保其在该层级中居中显示
3. 布局约束：
   - 节点按 `level_num` 从上往下排列
   - 同一 `level_num` 的节点按 `level_num_order` 从左到右排列
   - 网关节点单独占据一行，避免与其他节点重叠

**第四步：JSON文件更新**
1. 遍历计算出坐标的所有节点
2. 使用节点的唯一标识符（如 `target_id` 或 `id`）在原始 JSON 文件中定位对应的节点对象
3. 更新每个节点的 `x` 和 `y` 坐标属性
4. 保持 JSON 文件的其他数据结构和格式不变
5. 将更新后的数据写到 `processDesignModel_new.json` 文件

**实现要求：**
- 使用 Java 语言实现算法
- 确保代码具有良好的可读性和注释（使用简体中文注释）
- 处理边界情况（如孤立节点、循环引用等）
- 提供详细的日志输出，便于调试和验证结果
- 在修改文件前先备份原始数据

请先分析现有的 JSON 文件结构，然后实现完整的算法并直接修改 `processDesignModel.json` 文件中的坐标数据processDesignModel_new.json。