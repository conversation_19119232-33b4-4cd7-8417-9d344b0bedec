package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 连线布局分析器
 * 分析智能连线布局的效果
 */
public class ConnectionLayoutAnalyzer {
    
    public static void main(String[] args) {
        System.out.println("🔗 连线布局分析器");
        System.out.println("=".repeat(60));
        
        try {
            analyzeConnectionLayout("src/main/java/org/example/test3/processDesignModel_smart_connections.json");
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void analyzeConnectionLayout(String filePath) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            System.out.println("⚠️ 文件不存在: " + filePath);
            return;
        }
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(file);
        
        // 收集节点和连线信息
        Map<String, NodeInfo> nodes = new HashMap<>();
        List<ConnectionInfo> connections = new ArrayList<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                NodeInfo node = parseNodeInfo(key, value);
                nodes.put(key, node);
            } else if (isConnectionObject(value)) {
                ConnectionInfo connection = parseConnectionInfo(key, value);
                if (connection != null) {
                    connections.add(connection);
                }
            }
        }
        
        System.out.println("📊 基本统计:");
        System.out.println("   - 节点数量: " + nodes.size());
        System.out.println("   - 连线数量: " + connections.size());
        
        // 分析连线几何信息
        analyzeConnectionGeometry(connections);
        
        // 分析连线类型分布
        analyzeConnectionTypes(connections);
        
        // 分析连线避让效果
        analyzeConnectionAvoidance(connections, nodes);
        
        // 输出详细连线信息
        printDetailedConnectionInfo(connections, nodes);
    }
    
    private static void analyzeConnectionGeometry(List<ConnectionInfo> connections) {
        System.out.println("\n🔍 连线几何信息分析:");
        
        int withGeometry = 0;
        int withControlPoints = 0;
        int withPathType = 0;
        
        for (ConnectionInfo conn : connections) {
            if (conn.hasGeometry) {
                withGeometry++;
                
                if (conn.controlPointCount > 0) {
                    withControlPoints++;
                }
                
                if (conn.pathType != null && !conn.pathType.isEmpty()) {
                    withPathType++;
                }
            }
        }
        
        System.out.println("   - 包含几何信息的连线: " + withGeometry + "/" + connections.size());
        System.out.println("   - 包含控制点的连线: " + withControlPoints + "/" + connections.size());
        System.out.println("   - 包含路径类型的连线: " + withPathType + "/" + connections.size());
        
        if (withGeometry > 0) {
            System.out.println("   ✅ 智能连线布局已生效");
        } else {
            System.out.println("   ❌ 未发现智能连线布局信息");
        }
    }
    
    private static void analyzeConnectionTypes(List<ConnectionInfo> connections) {
        System.out.println("\n📋 连线类型分析:");
        
        Map<String, Integer> pathTypeCount = new HashMap<>();
        
        for (ConnectionInfo conn : connections) {
            if (conn.pathType != null) {
                pathTypeCount.put(conn.pathType, pathTypeCount.getOrDefault(conn.pathType, 0) + 1);
            }
        }
        
        if (pathTypeCount.isEmpty()) {
            System.out.println("   ⚠️ 未发现路径类型信息");
        } else {
            pathTypeCount.forEach((type, count) -> 
                System.out.println("   - " + type + " 路径: " + count + " 条"));
        }
    }
    
    private static void analyzeConnectionAvoidance(List<ConnectionInfo> connections, Map<String, NodeInfo> nodes) {
        System.out.println("\n🎯 连线避让效果分析:");
        
        int edgeConnections = 0;
        int centerConnections = 0;
        int withOffset = 0;
        
        for (ConnectionInfo conn : connections) {
            if (conn.hasGeometry) {
                // 检查是否从节点边缘连接
                NodeInfo sourceNode = nodes.get(conn.sourceId);
                NodeInfo targetNode = nodes.get(conn.targetId);
                
                if (sourceNode != null && targetNode != null) {
                    boolean isEdgeConnection = checkEdgeConnection(conn, sourceNode, targetNode);
                    
                    if (isEdgeConnection) {
                        edgeConnections++;
                    } else {
                        centerConnections++;
                    }
                    
                    // 检查是否有偏移（避让）
                    if (hasOffset(conn, sourceNode, targetNode)) {
                        withOffset++;
                    }
                }
            }
        }
        
        System.out.println("   - 边缘连接: " + edgeConnections + " 条");
        System.out.println("   - 中心连接: " + centerConnections + " 条");
        System.out.println("   - 包含偏移避让: " + withOffset + " 条");
        
        if (edgeConnections > centerConnections) {
            System.out.println("   ✅ 大部分连线使用边缘连接");
        } else {
            System.out.println("   ⚠️ 仍有较多连线使用中心连接");
        }
    }
    
    private static boolean checkEdgeConnection(ConnectionInfo conn, NodeInfo source, NodeInfo target) {
        // 简化检查：如果起点或终点不在节点中心，认为是边缘连接
        double sourceCenterX = source.x;
        double sourceCenterY = source.y;
        double targetCenterX = target.x;
        double targetCenterY = target.y;
        
        boolean sourceEdge = Math.abs(conn.startX - sourceCenterX) > 10 || 
                           Math.abs(conn.startY - sourceCenterY) > 10;
        boolean targetEdge = Math.abs(conn.endX - targetCenterX) > 10 || 
                           Math.abs(conn.endY - targetCenterY) > 10;
        
        return sourceEdge || targetEdge;
    }
    
    private static boolean hasOffset(ConnectionInfo conn, NodeInfo source, NodeInfo target) {
        // 检查是否有Y偏移或X偏移（避让机制）
        double expectedY = (source.y + target.y) / 2;
        double expectedX = (source.x + target.x) / 2;
        
        return Math.abs(conn.startY - source.y) > 5 || 
               Math.abs(conn.endY - target.y) > 5 ||
               Math.abs(conn.startX - source.x) > 5 || 
               Math.abs(conn.endX - target.x) > 5;
    }
    
    private static void printDetailedConnectionInfo(List<ConnectionInfo> connections, Map<String, NodeInfo> nodes) {
        System.out.println("\n📋 详细连线信息（前10条）:");
        
        int count = 0;
        for (ConnectionInfo conn : connections) {
            if (count >= 10) break;
            
            if (conn.hasGeometry) {
                NodeInfo source = nodes.get(conn.sourceId);
                NodeInfo target = nodes.get(conn.targetId);
                
                System.out.println(String.format("   🔗 %s -> %s:", conn.sourceId, conn.targetId));
                System.out.println(String.format("      起点: (%.1f, %.1f) [节点中心: (%.1f, %.1f)]", 
                    conn.startX, conn.startY, 
                    source != null ? source.x : 0, source != null ? source.y : 0));
                System.out.println(String.format("      终点: (%.1f, %.1f) [节点中心: (%.1f, %.1f)]", 
                    conn.endX, conn.endY,
                    target != null ? target.x : 0, target != null ? target.y : 0));
                System.out.println(String.format("      控制点: %d 个, 路径类型: %s", 
                    conn.controlPointCount, conn.pathType));
                
                count++;
            }
        }
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && value.has("geometry");
    }
    
    private static boolean isConnectionObject(JsonNode value) {
        return value.has("source") && value.has("target");
    }
    
    private static NodeInfo parseNodeInfo(String id, JsonNode nodeJson) {
        NodeInfo info = new NodeInfo();
        info.id = id;
        
        if (nodeJson.has("geometry")) {
            JsonNode geometry = nodeJson.get("geometry");
            if (geometry.has("x")) {
                info.x = geometry.get("x").asDouble();
            }
            if (geometry.has("y")) {
                info.y = geometry.get("y").asDouble();
            }
        }
        
        return info;
    }
    
    private static ConnectionInfo parseConnectionInfo(String id, JsonNode connJson) {
        ConnectionInfo info = new ConnectionInfo();
        info.id = id;
        
        // 解析source和target
        if (connJson.has("source") && connJson.get("source").has("id")) {
            info.sourceId = connJson.get("source").get("id").asText();
        }
        if (connJson.has("target") && connJson.get("target").has("id")) {
            info.targetId = connJson.get("target").get("id").asText();
        }
        
        // 解析几何信息
        if (connJson.has("geometry")) {
            JsonNode geometry = connJson.get("geometry");
            info.hasGeometry = true;
            
            if (geometry.has("startX")) info.startX = geometry.get("startX").asDouble();
            if (geometry.has("startY")) info.startY = geometry.get("startY").asDouble();
            if (geometry.has("endX")) info.endX = geometry.get("endX").asDouble();
            if (geometry.has("endY")) info.endY = geometry.get("endY").asDouble();
            
            if (geometry.has("controlPoints") && geometry.get("controlPoints").isArray()) {
                info.controlPointCount = geometry.get("controlPoints").size();
            }
            
            if (geometry.has("pathType")) {
                info.pathType = geometry.get("pathType").asText();
            }
        }
        
        return info.sourceId != null && info.targetId != null ? info : null;
    }
    
    static class NodeInfo {
        String id;
        double x, y;
    }
    
    static class ConnectionInfo {
        String id;
        String sourceId, targetId;
        boolean hasGeometry = false;
        double startX, startY, endX, endY;
        int controlPointCount = 0;
        String pathType;
    }
}
