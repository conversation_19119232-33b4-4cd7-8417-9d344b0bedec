package org.example.test3;

/**
 * 快速测试修改后的FlowLayoutProcessor
 */
public class QuickTest {
    public static void main(String[] args) {
        System.out.println("🧪 快速测试修改后的FlowLayoutProcessor");
        System.out.println("=".repeat(50));
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_test.json";
            
            processor.processFlowLayout(inputFile, outputFile);
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
