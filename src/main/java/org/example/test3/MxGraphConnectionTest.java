package org.example.test3;

/**
 * mxGraph连线格式测试程序
 * 测试符合mxGraph标准的智能连线布局
 */
public class MxGraphConnectionTest {
    public static void main(String[] args) {
        System.out.println("📊 mxGraph连线格式测试程序");
        System.out.println("测试目标：");
        System.out.println("1. 生成符合mxGraph标准的连线几何信息");
        System.out.println("2. 使用abspoints数组定义连线路径");
        System.out.println("3. 确保relative=1和其他mxGraph标准字段");
        System.out.println("4. 验证前端兼容性");
        System.out.println();
        
        System.out.println("mxGraph连线格式规范：");
        System.out.println("✅ x, y, width, height: 边界框（通常为0）");
        System.out.println("✅ relative: 相对定位标志（连线为1）");
        System.out.println("✅ abspoints: 绝对路径点数组");
        System.out.println("   - 第一个点：起点坐标");
        System.out.println("   - 中间点：路径控制点（避让点）");
        System.out.println("   - 最后一个点：终点坐标");
        System.out.println();
        
        System.out.println("预期JSON格式：");
        System.out.println("```json");
        System.out.println("{");
        System.out.println("  \"geometry\": {");
        System.out.println("    \"x\": 0,");
        System.out.println("    \"y\": 0,");
        System.out.println("    \"width\": 0,");
        System.out.println("    \"height\": 0,");
        System.out.println("    \"relative\": 1,");
        System.out.println("    \"abspoints\": [");
        System.out.println("      {\"x\": 起点X, \"y\": 起点Y},");
        System.out.println("      {\"x\": 控制点X, \"y\": 控制点Y},");
        System.out.println("      {\"x\": 终点X, \"y\": 终点Y}");
        System.out.println("    ]");
        System.out.println("  }");
        System.out.println("}");
        System.out.println("```");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_mxgraph.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行mxGraph格式的连线布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ mxGraph格式连线布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ 检查连线geometry字段包含mxGraph标准结构");
                System.out.println("2. ✅ 验证abspoints数组包含正确的路径点");
                System.out.println("3. ✅ 确认relative字段值为1");
                System.out.println("4. ✅ 检查x,y,width,height字段为0");
                
                System.out.println();
                System.out.println("🔍 调试信息验证：");
                System.out.println("- 检查控制台输出中的'计算智能连线布局'");
                System.out.println("- 确认'更新连线 XXX mxGraph几何信息'消息");
                System.out.println("- 验证路径点数量统计");
                
                System.out.println();
                System.out.println("📋 预期改进效果：");
                System.out.println("✅ 连线从节点边缘连接，不穿过中心");
                System.out.println("✅ 多条连线使用不同路径，避免重叠");
                System.out.println("✅ 智能避让算法，提供弯曲路径");
                System.out.println("✅ 完全兼容mxGraph前端渲染");
                
                System.out.println();
                System.out.println("🔧 连线类型处理：");
                System.out.println("- 水平同层：右边缘 → 左边缘 + Y偏移");
                System.out.println("- 垂直跨层：下边缘 → 上边缘 + X偏移");
                System.out.println("- 对角线：智能选择最佳连接点");
                System.out.println("- 避让路径：添加中间控制点");
                
            } else {
                System.err.println("❌ mxGraph格式连线布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ mxGraph连线格式测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. MxGraphConnectionGeometry类导入问题");
            System.err.println("   2. JSON序列化格式错误");
            System.err.println("   3. abspoints数组生成异常");
            System.err.println("   4. 连线路径计算错误");
        }
    }
}
