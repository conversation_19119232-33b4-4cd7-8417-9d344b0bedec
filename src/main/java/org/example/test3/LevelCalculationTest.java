package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 层级计算算法验证测试程序
 * 用于验证新的层级计算算法是否正确实现
 */
public class LevelCalculationTest {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    private static final String OUTPUT_FILE = "src/main/java/org/example/test3/processDesignModel_level_test.json";
    
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("🔬 层级计算算法验证测试程序");
        System.out.println("=".repeat(80));
        System.out.println("验证BFS循环迭代层级计算算法的正确性");
        System.out.println();
        
        try {
            // 1. 运行新的层级计算算法
            System.out.println("1. 🚀 运行新的层级计算算法...");
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            processor.processFlowLayout(INPUT_FILE, OUTPUT_FILE);
            
            // 2. 验证层级计算结果
            System.out.println("\n2. ✅ 验证层级计算结果...");
            validateLevelCalculation();
            
            // 3. 分析层级分布
            System.out.println("\n3. 📊 分析层级分布...");
            analyzeLevelDistribution();
            
            // 4. 验证有向无环图结构
            System.out.println("\n4. 🔍 验证有向无环图结构...");
            validateDAGStructure();
            
            // 5. 检查特殊节点处理
            System.out.println("\n5. ⚡ 检查特殊节点处理...");
            checkSpecialNodeHandling();
            
            System.out.println("\n🎉 层级计算算法验证完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证层级计算结果
     */
    private static void validateLevelCalculation() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            if (!outputFile.exists()) {
                System.err.println("❌ 输出文件不存在");
                return;
            }
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int totalNodes = 0;
            int startNodes = 0;
            int leveledNodes = 0;
            int isolatedNodes = 0;
            int minLevel = Integer.MAX_VALUE;
            int maxLevel = 0;
            
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isNodeObject(value)) {
                    totalNodes++;
                    
                    // 检查节点类型
                    JsonNode nodeTypeNode = value.get("nodeType");
                    if (nodeTypeNode != null) {
                        int nodeType = nodeTypeNode.asInt();
                        if (nodeType == 1) {
                            startNodes++;
                        }
                    }
                    
                    // 检查坐标以推断层级
                    JsonNode geometry = value.get("geometry");
                    if (geometry != null) {
                        double y = geometry.get("y").asDouble();
                        
                        // 根据Y坐标推断层级（基于SQL公式：Y = 150 + (level-1) * 400）
                        int inferredLevel = (int) Math.round((y - 150) / 400) + 1;
                        
                        if (inferredLevel > 0 && inferredLevel < 999) {
                            leveledNodes++;
                            minLevel = Math.min(minLevel, inferredLevel);
                            maxLevel = Math.max(maxLevel, inferredLevel);
                        } else if (inferredLevel >= 999) {
                            isolatedNodes++;
                        }
                    }
                }
            }
            
            System.out.println("✅ 层级计算验证结果:");
            System.out.println(String.format("   - 总节点数: %d", totalNodes));
            System.out.println(String.format("   - 开始节点数: %d", startNodes));
            System.out.println(String.format("   - 已分配层级节点: %d", leveledNodes));
            System.out.println(String.format("   - 孤立节点: %d", isolatedNodes));
            System.out.println(String.format("   - 层级范围: %d - %d", 
                    minLevel == Integer.MAX_VALUE ? 0 : minLevel, maxLevel));
            
            // 验证核心要求
            boolean hasValidStartNode = startNodes > 0 && minLevel == 1;
            boolean hasValidLevelStructure = leveledNodes > 0 && maxLevel > 0;
            boolean isolatedNodesHandled = isolatedNodes >= 0; // 允许存在孤立节点
            
            if (hasValidStartNode) {
                System.out.println("✅ 开始节点层级验证通过（Level 1）");
            } else {
                System.out.println("❌ 开始节点层级验证失败");
            }
            
            if (hasValidLevelStructure) {
                System.out.println("✅ 层级结构验证通过");
            } else {
                System.out.println("❌ 层级结构验证失败");
            }
            
            if (isolatedNodes == 0) {
                System.out.println("✅ 无孤立节点，所有节点都可达");
            } else {
                System.out.println(String.format("⚠️  存在 %d 个孤立节点", isolatedNodes));
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证层级计算时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 分析层级分布
     */
    private static void analyzeLevelDistribution() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            Map<Integer, List<String>> levelDistribution = new HashMap<>();
            
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isNodeObject(value)) {
                    JsonNode geometry = value.get("geometry");
                    if (geometry != null) {
                        double y = geometry.get("y").asDouble();
                        int level = (int) Math.round((y - 150) / 400) + 1;
                        
                        levelDistribution.computeIfAbsent(level, k -> new ArrayList<>()).add(key);
                    }
                }
            }
            
            System.out.println("📊 层级分布分析:");
            
            // 按层级排序显示
            List<Integer> sortedLevels = new ArrayList<>(levelDistribution.keySet());
            Collections.sort(sortedLevels);
            
            for (Integer level : sortedLevels) {
                List<String> nodesInLevel = levelDistribution.get(level);
                if (level < 999) {
                    System.out.println(String.format("   Level %d: %d 个节点", level, nodesInLevel.size()));
                    
                    // 显示前5个节点
                    if (nodesInLevel.size() <= 5) {
                        System.out.println(String.format("     节点: %s", String.join(", ", nodesInLevel)));
                    } else {
                        List<String> firstFive = nodesInLevel.subList(0, 5);
                        System.out.println(String.format("     节点: %s... (共%d个)", 
                                String.join(", ", firstFive), nodesInLevel.size()));
                    }
                } else {
                    System.out.println(String.format("   孤立节点: %d 个", nodesInLevel.size()));
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 分析层级分布时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 验证有向无环图结构
     */
    private static void validateDAGStructure() {
        try {
            File inputFile = new File(INPUT_FILE);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode inputJson = mapper.readTree(inputFile);
            
            // 构建图结构
            Map<String, List<String>> graph = new HashMap<>();
            Set<String> allNodes = new HashSet<>();
            
            Iterator<Map.Entry<String, JsonNode>> fields = inputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isNodeObject(value)) {
                    allNodes.add(key);
                    graph.put(key, new ArrayList<>());
                } else if (isConnectionObject(value)) {
                    JsonNode source = value.get("source");
                    JsonNode target = value.get("target");
                    
                    if (source != null && target != null) {
                        String sourceId = source.get("id").asText();
                        String targetId = target.get("id").asText();
                        
                        // 排除recall连线
                        if (!"recall".equals(targetId)) {
                            graph.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
                        }
                    }
                }
            }
            
            // 检查是否存在环
            boolean hasCycle = detectCycle(graph, allNodes);
            
            System.out.println("🔍 有向无环图验证:");
            System.out.println(String.format("   - 总节点数: %d", allNodes.size()));
            System.out.println(String.format("   - 总边数: %d", graph.values().stream().mapToInt(List::size).sum()));
            
            if (!hasCycle) {
                System.out.println("✅ 图结构为有向无环图（DAG）");
            } else {
                System.out.println("❌ 检测到环，图结构不是DAG");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证DAG结构时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 检测图中是否存在环
     */
    private static boolean detectCycle(Map<String, List<String>> graph, Set<String> allNodes) {
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        for (String node : allNodes) {
            if (!visited.contains(node)) {
                if (dfsHasCycle(node, graph, visited, recursionStack)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * DFS检测环
     */
    private static boolean dfsHasCycle(String node, Map<String, List<String>> graph, 
                                      Set<String> visited, Set<String> recursionStack) {
        visited.add(node);
        recursionStack.add(node);
        
        List<String> neighbors = graph.get(node);
        if (neighbors != null) {
            for (String neighbor : neighbors) {
                if (!visited.contains(neighbor)) {
                    if (dfsHasCycle(neighbor, graph, visited, recursionStack)) {
                        return true;
                    }
                } else if (recursionStack.contains(neighbor)) {
                    return true; // 发现环
                }
            }
        }
        
        recursionStack.remove(node);
        return false;
    }
    
    /**
     * 检查特殊节点处理
     */
    private static void checkSpecialNodeHandling() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int recallNodes = 0;
            int byStartNodes = 0;
            int specialNodeCoordinates = 0;
            
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isNodeObject(value)) {
                    // 检查recall节点
                    if ("recall".equals(key)) {
                        recallNodes++;
                        JsonNode geometry = value.get("geometry");
                        if (geometry != null) {
                            specialNodeCoordinates++;
                        }
                    }
                    
                    // 检查byStart节点
                    if ("byStart".equals(key)) {
                        byStartNodes++;
                        JsonNode geometry = value.get("geometry");
                        if (geometry != null) {
                            specialNodeCoordinates++;
                        }
                    }
                }
            }
            
            System.out.println("⚡ 特殊节点处理检查:");
            System.out.println(String.format("   - Recall节点: %d 个", recallNodes));
            System.out.println(String.format("   - byStart节点: %d 个", byStartNodes));
            System.out.println(String.format("   - 特殊节点坐标: %d 个", specialNodeCoordinates));
            
            if (recallNodes > 0 || byStartNodes > 0) {
                System.out.println("✅ 特殊节点已正确识别和处理");
            } else {
                System.out.println("ℹ️  未发现特殊节点");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 检查特殊节点时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为节点对象
     */
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    /**
     * 判断是否为连线对象
     */
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target")) ||
               value.has("abspoints");
    }
}
