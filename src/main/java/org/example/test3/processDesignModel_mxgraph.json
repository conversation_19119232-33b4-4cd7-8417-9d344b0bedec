{"0": {"id": "0", "parent": null, "source": null, "target": null, "children": null, "mxObjectId": "mxCell#41"}, "1": {"id": "1", "parent": null, "source": null, "target": null, "children": null, "mxObjectId": "mxCell#40"}, "init": {"id": "init", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 350.0, "width": 180.0, "height": 110.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "流程初始化", "nodeType": 2, "mxObjectId": "mxCell#42", "connectable": "true", "level_num": 2, "level_num_order": 1}, "start": {"id": "start", "edges": null, "style": "shadow=0;fillColor=#1DCC8F;strokeColor=white;fontColor=#FFFFFF;rounded=1;arcSize=50;fontSize=14;fontFamily=Microsoft YaHei;right;html=1;", "value": "<p style=\"margin:0;padding:0 0 0 18px;background:url(static/flowDesigner/stencils/clipart/start.png) no-repeat left center;background-size: 14px 14px;\">Start</p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 150.0, "width": 150.0, "height": 40.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "Start", "nodeType": 1, "mxObjectId": "mxCell#49", "level_num": 1, "level_num_order": 1}, "recall": {"id": "recall", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": -850.0, "y": 550.0, "width": 180.0, "height": 142.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "重新提交", "nodeType": 4, "mxObjectId": "mxCell#51", "level_num": 3, "level_num_order": 2}, "byStart": {"id": "byStart", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 550.0, "width": 180.0, "height": 110.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "byStart_fromEBPM_byStart", "nodeType": 9, "mxObjectId": "mxCell#52", "connectable": "true", "level_num": 3, "level_num_order": 1}, "startToInit": {"id": "startToInit", "edge": "true", "value": "", "parent": null, "source": {"id": "start", "edges": null, "style": "shadow=0;fillColor=#1DCC8F;strokeColor=white;fontColor=#FFFFFF;rounded=1;arcSize=50;fontSize=14;fontFamily=Microsoft YaHei;right;html=1;", "value": "<p style=\"margin:0;padding:0 0 0 18px;background:url(static/flowDesigner/stencils/clipart/start.png) no-repeat left center;background-size: 14px 14px;\">Start</p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 214, "y": 276, "width": 150, "height": 40, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "Start", "nodeType": 1, "mxObjectId": "mxCell#49"}, "target": {"id": "init", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 214, "y": 676, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "流程初始化", "nodeType": 2, "mxObjectId": "mxCell#42", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 210.0, "y": 170.0}, {"x": 210.0, "y": 232.5}, {"x": 210.0, "y": 232.5}, {"x": 210.0, "y": 295.0}]}, "nodeType": 99, "mxObjectId": "mxCell#53"}, "initToClient": {"id": "initToClient", "edge": "true", "parent": null, "source": {"id": "init", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 214, "y": 676, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "流程初始化", "nodeType": 2, "mxObjectId": "mxCell#42", "connectable": "true"}, "target": {"id": "byStart", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 564, "y": 576, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "byStart_fromEBPM_byStart", "nodeType": 9, "mxObjectId": "mxCell#52", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 230.0, "y": 405.0}, {"x": 230.0, "y": 450.0}, {"x": 230.0, "y": 450.0}, {"x": 230.0, "y": 495.0}]}, "nodeType": 99, "mxObjectId": "mxCell#54"}, "initToRecall": {"id": "initToRecall", "edge": "true", "value": "false", "parent": null, "source": {"id": "init", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/init.png) no-repeat left center;background-size: 14px 14px;\">流程初始化</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"流程初始化\">流程初始化</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 214, "y": 676, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "流程初始化", "nodeType": 2, "mxObjectId": "mxCell#42", "connectable": "true"}, "target": {"id": "recall", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": -86, "y": 426, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "重新提交", "nodeType": 4, "mxObjectId": "mxCell#51"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 60.0, "y": 350.0}, {"x": -400.0, "y": 480.0}, {"x": -760.0, "y": 550.0}]}, "nodeType": 99, "mxObjectId": "mxCell#55"}, "recallToClient": {"id": "recallToClient", "edge": "true", "value": "reSubmit", "parent": null, "source": {"id": "recall", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": -86, "y": 426, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "重新提交", "nodeType": 4, "mxObjectId": "mxCell#51"}, "target": {"id": "byStart", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 564, "y": 576, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "byStart_fromEBPM_byStart", "nodeType": 9, "mxObjectId": "mxCell#52", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": -760.0, "y": 565.0}, {"x": -350.0, "y": 565.0}, {"x": -350.0, "y": 565.0}, {"x": 60.0, "y": 565.0}]}, "nodeType": 99, "mxObjectId": "mxCell#56"}, "06f9fe94-c1be-0959-1b40-459e1c343d1b": {"id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 1350.0, "width": 180.0, "height": 142.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "nodeType": 6, "mxObjectId": "mxCell#57", "connectable": "true", "level_num": 7, "level_num_order": 1}, "5bfb34a8-a16d-2d3e-5617-809bcbda5695": {"id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 1150.0, "width": 180.0, "height": 142.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "nodeType": 6, "mxObjectId": "mxCell#58", "connectable": "true", "level_num": 6, "level_num_order": 1}, "e53d74da-8f5b-8b3e-a1b8-a901a777ed03": {"id": "e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 750.0, "width": 180.0, "height": 110.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "nodeType": 9, "mxObjectId": "mxCell#60", "connectable": "true", "level_num": 4, "level_num_order": 1}, "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd": {"id": "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 150.0, "y": 950.0, "width": 180.0, "height": 110.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "nodeType": 9, "mxObjectId": "mxCell#61", "connectable": "true", "level_num": 5, "level_num_order": 1}, "line0ce090f8-31a5-fc15-f281-6137e6a0b797": {"id": "line0ce090f8-31a5-fc15-f281-6137e6a0b797", "edge": "true", "name": "审核不通过0ce090f8-31a5-fc15-f281-6137e6a0b797", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "value": "审核不通过0ce090f8-31a5-fc15-f281-6137e6a0b797", "parent": null, "source": {"id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 2156, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "nodeType": 6, "mxObjectId": "mxCell#57", "connectable": "true"}, "target": {"id": "recall", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": -86, "y": 426, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "重新提交", "nodeType": 4, "mxObjectId": "mxCell#51"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 60.0, "y": 1350.0}, {"x": -400.0, "y": 920.0}, {"x": -760.0, "y": 550.0}]}, "nodeType": 99, "mxObjectId": "mxCell#43"}, "line575b302a-117b-ea86-1594-11998c03958e": {"id": "line575b302a-117b-ea86-1594-11998c03958e", "edge": "true", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "parent": null, "source": {"id": "e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 636, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "nodeType": 9, "mxObjectId": "mxCell#60", "connectable": "true"}, "target": {"id": "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 1036, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "nodeType": 9, "mxObjectId": "mxCell#61", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 190.0, "y": 805.0}, {"x": 190.0, "y": 850.0}, {"x": 190.0, "y": 850.0}, {"x": 190.0, "y": 895.0}]}, "nodeType": 99, "mxObjectId": "mxCell#44"}, "line802fef4f-aa7d-e099-34a6-e545b76c4b6e": {"id": "line802fef4f-aa7d-e099-34a6-e545b76c4b6e", "edge": "true", "name": "审核通过802fef4f-aa7d-e099-34a6-e545b76c4b6e", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "value": "审核通过802fef4f-aa7d-e099-34a6-e545b76c4b6e", "parent": null, "source": {"id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 1516, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "nodeType": 6, "mxObjectId": "mxCell#58", "connectable": "true"}, "target": {"id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 2156, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "nodeType": 6, "mxObjectId": "mxCell#57", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 230.0, "y": 1221.0}, {"x": 230.0, "y": 1250.0}, {"x": 230.0, "y": 1250.0}, {"x": 230.0, "y": 1279.0}]}, "nodeType": 99, "mxObjectId": "mxCell#45"}, "line9f50482b-68e6-dcc6-27ab-619343addd64": {"id": "line9f50482b-68e6-dcc6-27ab-619343addd64", "edge": "true", "name": "审核不通过9f50482b-68e6-dcc6-27ab-619343addd64", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "value": "审核不通过9f50482b-68e6-dcc6-27ab-619343addd64", "parent": null, "source": {"id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 1516, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "nodeType": 6, "mxObjectId": "mxCell#58", "connectable": "true"}, "target": {"id": "recall", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"k2-node-title\" ><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/recall.png) no-repeat left center;background-size: 14px 14px;\">重新提交</span></p><ul class=\"k2-node-container\"><li class=\"k2-node-item\" title=\"节点初始化\">节点初始化</li><li class=\"k2-node-item\" title=\"重新提交\">重新提交</li><li class=\"k2-node-item\" title=\"节点结束\">节点结束</li></ul>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": -86, "y": 426, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "重新提交", "nodeType": 4, "mxObjectId": "mxCell#51"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 60.0, "y": 1150.0}, {"x": -400.0, "y": 820.0}, {"x": -760.0, "y": 550.0}]}, "nodeType": 99, "mxObjectId": "mxCell#46"}, "lineea70b8e0-4e08-2563-1457-5aa7d133b722": {"id": "lineea70b8e0-4e08-2563-1457-5aa7d133b722", "edge": "true", "name": "申请提交完成ea70b8e0-4e08-2563-1457-5aa7d133b722", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "value": "申请提交完成ea70b8e0-4e08-2563-1457-5aa7d133b722", "parent": null, "source": {"id": "fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 1036, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "服务-发起银行放款申请_fromEBPM_fc1a66c0-2ab2-6a2d-7cd1-3ce14797eefd", "nodeType": 9, "mxObjectId": "mxCell#61", "connectable": "true"}, "target": {"id": "5bfb34a8-a16d-2d3e-5617-809bcbda5695", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 1516, "width": 180, "height": 142, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审核银行放款申请_fromEBPM_5bfb34a8-a16d-2d3e-5617-809bcbda5695", "nodeType": 6, "mxObjectId": "mxCell#58", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 150.0, "y": 1005.0}, {"x": 150.0, "y": 1042.0}, {"x": 150.0, "y": 1042.0}, {"x": 150.0, "y": 1079.0}]}, "nodeType": 99, "mxObjectId": "mxCell#47"}, "occid7938771f-7426-42e7-8010-edf92d0b3694": {"id": "occid7938771f-7426-42e7-8010-edf92d0b3694", "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694</span></p>", "vertex": "true", "geometry": {"x": 150.0, "y": 1550.0, "width": 180.0, "height": 110.0, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694", "nodeType": "9", "mxObjectId": "mxCell#85", "connectable": "true", "level_num": 8, "level_num_order": 1}, "lineec79b8f2-3474-336-42e7-8010-edf92d0b3694": {"id": "lineec79b8f2-3474-336-42e7-8010-edf92d0b3694", "edge": true, "name": "审核通过-流程结束ec79b8f2-3474-336-42e7-8010-edf92d0b3694", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "value": "审核通过-流程结束ec79b8f2-3474-336-42e7-8010-edf92d0b3694", "source": {"id": "06f9fe94-c1be-0959-1b40-459e1c343d1b", "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b</span></p>", "vertex": "true", "geometry": {"x": 1046, "y": 2156, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "审批银行放款申请_fromEBPM_06f9fe94-c1be-0959-1b40-459e1c343d1b", "nodeType": "6", "mxObjectId": "mxCell#85", "connectable": "true"}, "target": {"id": "occid7938771f-7426-42e7-8010-edf92d0b3694", "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694</span></p>", "vertex": "true", "geometry": {"x": 1346, "y": 2796, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "流程已结束_fromEBPM_occid7938771f-7426-42e7-8010-edf92d0b3694", "nodeType": "9", "mxObjectId": "mxCell#85", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 170.0, "y": 1421.0}, {"x": 170.0, "y": 1458.0}, {"x": 170.0, "y": 1458.0}, {"x": 170.0, "y": 1495.0}]}, "nodeType": 99, "mxObjectId": "mxCell#85", "connectable": "true"}, "linebyStarte53d74da-8f5b-8b3e-a1b8-a901a777ed03": {"id": "linebyStarte53d74da-8f5b-8b3e-a1b8-a901a777ed03", "edge": "true", "style": "edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;strokeColor=#979AA3", "parent": null, "source": {"id": "byStart", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\"></span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 564, "y": 576, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "byStart_fromEBPM_byStart", "nodeType": 9, "mxObjectId": "mxCell#52", "connectable": "true"}, "target": {"id": "e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "edges": null, "style": "shadow=0;rounded=1;arcSize=20;rotatable=1;arcSize=20;verticalAlign=top;align=left;overflow=fill;fontSize=16;strokeColor=white;fontFamily=Microsoft YaHei;html=1;", "value": "<p class=\"other-node-title\"><span style=\"background:transparent url(static/flowDesigner/stencils/clipart/user_white.png) no-repeat left center;background-size: 14px 14px;\">用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03</span></p>", "parent": null, "source": null, "target": null, "vertex": "true", "geometry": {"x": 1046, "y": 636, "width": 180, "height": 110, "TRANSLATE_CONTROL_POINTS": "true"}, "nodeName": "用于办理银行放款申请业务_fromEBPM_e53d74da-8f5b-8b3e-a1b8-a901a777ed03", "nodeType": 9, "mxObjectId": "mxCell#60", "connectable": "true"}, "geometry": {"x": 0.0, "y": 0.0, "width": 0.0, "height": 0.0, "relative": 1, "abspoints": [{"x": 170.0, "y": 605.0}, {"x": 170.0, "y": 650.0}, {"x": 170.0, "y": 650.0}, {"x": 170.0, "y": 695.0}]}, "nodeType": 99, "mxObjectId": "mxCell#50"}}