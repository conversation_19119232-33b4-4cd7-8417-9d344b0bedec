package org.example.test3;

/**
 * 简化的直线连接测试
 * 验证代码修改是否正确
 */
public class DirectLineTestSimple {
    public static void main(String[] args) {
        System.out.println("📏 简化直线连接测试");

        try {
            // 创建处理器实例
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            
            // 测试输入输出文件路径
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_direct_lines.json";
            
            System.out.println("📁 输入: " + inputFile);
            System.out.println("📁 输出: " + outputFile);
            System.out.println();
            
            // 执行处理
            processor.processFlowLayout(inputFile, outputFile);
            
            System.out.println("✅ 处理完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
