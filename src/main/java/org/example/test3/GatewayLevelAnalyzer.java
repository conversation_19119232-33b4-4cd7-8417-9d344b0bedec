package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 网关节点层级分析器
 * 专门分析网关节点的层级分配和独占性
 */
public class GatewayLevelAnalyzer {
    
    public static void main(String[] args) {
        System.out.println("🚪 网关节点层级分析器");
        System.out.println("=".repeat(60));
        
        try {
            // 分析原始文件
            System.out.println("📋 分析原始文件:");
            analyzeGatewayLevels("src/main/java/org/example/test3/processDesignModel.json", "原始");
            
            System.out.println();
            
            // 分析修复后的文件
            System.out.println("📋 分析修复后文件:");
            analyzeGatewayLevels("src/main/java/org/example/test3/processDesignModel_gateway_fix.json", "修复后");
            
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void analyzeGatewayLevels(String filePath, String fileType) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            System.out.println("   ⚠️ 文件不存在: " + filePath);
            return;
        }
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(file);
        
        // 收集所有网关节点
        List<GatewayInfo> gatewayNodes = new ArrayList<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isGatewayNode(value)) {
                GatewayInfo gateway = parseGatewayInfo(key, value);
                gatewayNodes.add(gateway);
            }
        }
        
        System.out.println(String.format("   🚪 %s文件中发现 %d 个网关节点", fileType, gatewayNodes.size()));
        
        if (gatewayNodes.isEmpty()) {
            return;
        }
        
        // 按层级排序
        gatewayNodes.sort((a, b) -> Integer.compare(a.levelNum, b.levelNum));
        
        // 输出网关节点详情
        System.out.println("   📊 网关节点详情:");
        for (GatewayInfo gateway : gatewayNodes) {
            System.out.println(String.format("      - %s: level_num=%d, level_order=%d, 坐标=(%.0f, %.0f)", 
                gateway.id, gateway.levelNum, gateway.levelOrder, gateway.x, gateway.y));
        }
        
        // 检查层级冲突
        System.out.println("   🔍 层级冲突检查:");
        Map<Integer, List<String>> levelGroups = new HashMap<>();
        for (GatewayInfo gateway : gatewayNodes) {
            levelGroups.computeIfAbsent(gateway.levelNum, k -> new ArrayList<>()).add(gateway.id);
        }
        
        boolean hasConflicts = false;
        for (Map.Entry<Integer, List<String>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<String> gateways = entry.getValue();
            
            if (gateways.size() > 1) {
                System.out.println(String.format("      ❌ 层级 %d 冲突: %s", level, String.join(", ", gateways)));
                hasConflicts = true;
            } else {
                System.out.println(String.format("      ✅ 层级 %d 独占: %s", level, gateways.get(0)));
            }
        }
        
        if (!hasConflicts) {
            System.out.println("   ✅ 所有网关节点都独占各自的层级");
        }
        
        // 检查坐标分离
        System.out.println("   📐 坐标分离检查:");
        for (int i = 0; i < gatewayNodes.size() - 1; i++) {
            GatewayInfo current = gatewayNodes.get(i);
            GatewayInfo next = gatewayNodes.get(i + 1);
            
            double yDistance = Math.abs(next.y - current.y);
            System.out.println(String.format("      %s -> %s: Y距离 = %.0f像素", 
                current.id, next.id, yDistance));
            
            if (yDistance < 300) {
                System.out.println("         ⚠️ Y距离过小，可能存在视觉重叠");
            } else {
                System.out.println("         ✅ Y距离合适，垂直分离良好");
            }
        }
    }
    
    private static boolean isGatewayNode(JsonNode value) {
        return value.has("nodeType") && 
               value.get("nodeType").asInt() == 13 &&
               value.has("geometry");
    }
    
    private static GatewayInfo parseGatewayInfo(String id, JsonNode nodeJson) {
        GatewayInfo info = new GatewayInfo();
        info.id = id;
        
        if (nodeJson.has("nodeName")) {
            info.nodeName = nodeJson.get("nodeName").asText();
        }
        
        if (nodeJson.has("level_num")) {
            info.levelNum = nodeJson.get("level_num").asInt();
        }
        
        if (nodeJson.has("level_num_order")) {
            info.levelOrder = nodeJson.get("level_num_order").asInt();
        }
        
        if (nodeJson.has("geometry")) {
            JsonNode geometry = nodeJson.get("geometry");
            if (geometry.has("x")) {
                info.x = geometry.get("x").asDouble();
            }
            if (geometry.has("y")) {
                info.y = geometry.get("y").asDouble();
            }
        }
        
        return info;
    }
    
    static class GatewayInfo {
        String id;
        String nodeName;
        int levelNum;
        int levelOrder;
        double x;
        double y;
    }
}
