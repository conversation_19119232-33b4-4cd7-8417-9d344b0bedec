package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 结束节点分析器
 * 分析结束节点的位置和层级分配
 */
public class EndNodeAnalyzer {
    
    public static void main(String[] args) {
        System.out.println("🏁 结束节点位置分析器");
        System.out.println("=".repeat(60));
        
        try {
            analyzeEndNodes("src/main/java/org/example/test3/processDesignModel.json");
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void analyzeEndNodes(String filePath) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(filePath));
        
        // 收集所有节点
        Map<String, NodeInfo> allNodes = new HashMap<>();
        Map<String, List<String>> connections = new HashMap<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                NodeInfo node = parseNodeInfo(key, value);
                allNodes.put(key, node);
            } else if (isConnectionObject(value)) {
                parseConnection(value, connections);
            }
        }
        
        System.out.println("📊 节点统计:");
        System.out.println("   - 总节点数: " + allNodes.size());
        System.out.println("   - 总连线数: " + connections.size());
        
        // 分析节点类型分布
        analyzeNodeTypes(allNodes);
        
        // 查找结束节点
        findEndNodes(allNodes, connections);
        
        // 分析出度为0的节点
        analyzeZeroOutDegreeNodes(allNodes, connections);
        
        // 分析层级分布
        analyzeLevelDistribution(allNodes);
    }
    
    private static void analyzeNodeTypes(Map<String, NodeInfo> nodes) {
        System.out.println("\n📋 节点类型分布:");
        
        Map<Integer, List<String>> typeGroups = new HashMap<>();
        for (Map.Entry<String, NodeInfo> entry : nodes.entrySet()) {
            NodeInfo node = entry.getValue();
            typeGroups.computeIfAbsent(node.nodeType, k -> new ArrayList<>()).add(entry.getKey());
        }
        
        typeGroups.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int type = entry.getKey();
                List<String> nodeIds = entry.getValue();
                String typeName = getNodeTypeName(type);
                System.out.println(String.format("   - %s (类型%d): %d个", typeName, type, nodeIds.size()));
                
                if (type == 3) { // 结束节点
                    System.out.println("     结束节点详情:");
                    for (String nodeId : nodeIds) {
                        NodeInfo node = nodes.get(nodeId);
                        System.out.println(String.format("       * %s: %s", nodeId, node.nodeName));
                    }
                }
            });
    }
    
    private static void findEndNodes(Map<String, NodeInfo> nodes, Map<String, List<String>> connections) {
        System.out.println("\n🏁 结束节点分析:");
        
        // 1. 查找nodeType=3的节点
        List<String> typeEndNodes = new ArrayList<>();
        for (Map.Entry<String, NodeInfo> entry : nodes.entrySet()) {
            if (entry.getValue().nodeType == 3) {
                typeEndNodes.add(entry.getKey());
            }
        }
        
        // 2. 查找id="end"的节点
        List<String> idEndNodes = new ArrayList<>();
        if (nodes.containsKey("end")) {
            idEndNodes.add("end");
        }
        
        // 3. 查找出度为0的节点
        Set<String> nodesWithOutgoing = new HashSet<>();
        for (List<String> targets : connections.values()) {
            nodesWithOutgoing.addAll(targets);
        }
        
        List<String> zeroOutDegreeNodes = new ArrayList<>();
        for (String nodeId : nodes.keySet()) {
            if (!connections.containsKey(nodeId) || connections.get(nodeId).isEmpty()) {
                zeroOutDegreeNodes.add(nodeId);
            }
        }
        
        System.out.println("   按类型查找的结束节点 (nodeType=3): " + typeEndNodes.size());
        for (String nodeId : typeEndNodes) {
            NodeInfo node = nodes.get(nodeId);
            System.out.println(String.format("     - %s: %s", nodeId, node.nodeName));
        }
        
        System.out.println("   按ID查找的结束节点 (id='end'): " + idEndNodes.size());
        for (String nodeId : idEndNodes) {
            NodeInfo node = nodes.get(nodeId);
            System.out.println(String.format("     - %s: %s", nodeId, node.nodeName));
        }
        
        System.out.println("   出度为0的节点: " + zeroOutDegreeNodes.size());
        for (String nodeId : zeroOutDegreeNodes) {
            NodeInfo node = nodes.get(nodeId);
            System.out.println(String.format("     - %s: %s (类型%d)", nodeId, node.nodeName, node.nodeType));
        }
    }
    
    private static void analyzeZeroOutDegreeNodes(Map<String, NodeInfo> nodes, Map<String, List<String>> connections) {
        System.out.println("\n📤 出度分析:");
        
        for (Map.Entry<String, NodeInfo> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            NodeInfo node = entry.getValue();
            
            int outDegree = connections.containsKey(nodeId) ? connections.get(nodeId).size() : 0;
            
            if (outDegree == 0) {
                System.out.println(String.format("   出度为0: %s (%s, 类型%d)", 
                    nodeId, node.nodeName, node.nodeType));
            }
        }
    }
    
    private static void analyzeLevelDistribution(Map<String, NodeInfo> nodes) {
        System.out.println("\n📊 当前层级分布分析:");
        
        // 按原始坐标分析层级
        Map<Double, List<String>> yGroups = new HashMap<>();
        for (Map.Entry<String, NodeInfo> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            NodeInfo node = entry.getValue();
            
            double y = Math.round(node.y / 100) * 100; // 按100像素分组
            yGroups.computeIfAbsent(y, k -> new ArrayList<>()).add(nodeId);
        }
        
        yGroups.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                double y = entry.getKey();
                List<String> nodeIds = entry.getValue();
                
                System.out.println(String.format("   Y坐标 %.0f 附近: %d个节点", y, nodeIds.size()));
                for (String nodeId : nodeIds) {
                    NodeInfo node = nodes.get(nodeId);
                    String typeName = getNodeTypeName(node.nodeType);
                    System.out.println(String.format("     - %s: %s (%s)", nodeId, node.nodeName, typeName));
                }
            });
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private static NodeInfo parseNodeInfo(String id, JsonNode nodeJson) {
        NodeInfo info = new NodeInfo();
        info.id = id;
        
        if (nodeJson.has("nodeName")) {
            info.nodeName = nodeJson.get("nodeName").asText();
        }
        
        if (nodeJson.has("nodeType")) {
            info.nodeType = nodeJson.get("nodeType").asInt();
        }
        
        if (nodeJson.has("geometry")) {
            JsonNode geometry = nodeJson.get("geometry");
            if (geometry.has("x")) {
                info.x = geometry.get("x").asDouble();
            }
            if (geometry.has("y")) {
                info.y = geometry.get("y").asDouble();
            }
        }
        
        return info;
    }
    
    private static void parseConnection(JsonNode connectionJson, Map<String, List<String>> connections) {
        if (connectionJson.has("source") && connectionJson.has("target")) {
            JsonNode source = connectionJson.get("source");
            JsonNode target = connectionJson.get("target");
            
            if (source.has("id") && target.has("id")) {
                String sourceId = source.get("id").asText();
                String targetId = target.get("id").asText();
                
                connections.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
            }
        }
    }
    
    private static String getNodeTypeName(int nodeType) {
        switch (nodeType) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 3: return "结束节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            default: return "未知类型";
        }
    }
    
    static class NodeInfo {
        String id;
        String nodeName;
        int nodeType;
        double x;
        double y;
    }
}
