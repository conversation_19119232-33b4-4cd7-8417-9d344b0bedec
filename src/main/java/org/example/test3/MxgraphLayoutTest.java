package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.Iterator;
import java.util.Map;

/**
 * SQL算法移植验证测试程序
 * 用于验证Java实现是否完全符合ProcessModelHierarchicalLayout_Fixed.sql的算法
 */
public class SQLAlgorithmTest {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    private static final String OUTPUT_FILE = "src/main/java/org/example/test3/processDesignModel_sql_algorithm.json";
    
    // SQL算法常量（与SQL文件一致）
    private static final int BASE_COORDINATE_OFFSET = 150;
    private static final int HORIZONTAL_SPACING = 500;
    private static final int VERTICAL_SPACING = 400;
    
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("🔬 SQL算法移植验证测试程序");
        System.out.println("=".repeat(80));
        System.out.println("基于 ProcessModelHierarchicalLayout_Fixed.sql 算法验证");
        System.out.println();
        
        try {
            // 1. 运行SQL算法移植的布局处理
            System.out.println("1. 🚀 运行SQL算法移植的布局处理...");
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            processor.processFlowLayout(INPUT_FILE, OUTPUT_FILE);
            
            // 2. 验证SQL算法实现
            System.out.println("\n2. ✅ 验证SQL算法实现...");
            validateSQLAlgorithm();
            
            // 3. 分析坐标计算精度
            System.out.println("\n3. 📐 分析坐标计算精度...");
            analyzeCoordinatePrecision();
            
            // 4. 验证层级排序逻辑
            System.out.println("\n4. 📊 验证层级排序逻辑...");
            validateLevelSorting();
            
            System.out.println("\n🎉 SQL算法移植验证完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证SQL算法实现
     */
    private static void validateSQLAlgorithm() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            if (!outputFile.exists()) {
                System.err.println("❌ 输出文件不存在");
                return;
            }
            
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            int nodeCount = 0;
            int validCoordinateCount = 0;
            int invalidCoordinateCount = 0;
            
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isNodeObject(value)) {
                    nodeCount++;
                    
                    // 验证坐标是否符合SQL算法
                    if (validateNodeCoordinates(key, value)) {
                        validCoordinateCount++;
                    } else {
                        invalidCoordinateCount++;
                    }
                }
            }
            
            System.out.println("✅ SQL算法验证结果:");
            System.out.println(String.format("   - 总节点数: %d", nodeCount));
            System.out.println(String.format("   - 符合SQL算法的节点: %d", validCoordinateCount));
            System.out.println(String.format("   - 不符合SQL算法的节点: %d", invalidCoordinateCount));
            System.out.println(String.format("   - 算法符合率: %.1f%%", 
                    (double) validCoordinateCount / nodeCount * 100));
            
            if (invalidCoordinateCount == 0) {
                System.out.println("🎯 完美！所有节点坐标都符合SQL算法要求");
            } else {
                System.out.println("⚠️  存在不符合SQL算法的节点，需要检查实现");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证SQL算法时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 验证单个节点坐标是否符合SQL算法
     */
    private static boolean validateNodeCoordinates(String nodeId, JsonNode nodeValue) {
        try {
            JsonNode geometry = nodeValue.get("geometry");
            if (geometry == null) return false;
            
            double x = geometry.get("x").asDouble();
            double y = geometry.get("y").asDouble();
            
            // 检查是否符合SQL坐标公式
            // X = 150 + (level_order - 1) * 500
            // Y = 150 + (level_num - 1) * 400
            
            // 计算可能的level_num和level_order
            int possibleLevel = (int) Math.round((y - BASE_COORDINATE_OFFSET) / VERTICAL_SPACING) + 1;
            int possibleOrder = (int) Math.round((x - BASE_COORDINATE_OFFSET) / HORIZONTAL_SPACING) + 1;
            
            // 验证计算结果
            double expectedX = BASE_COORDINATE_OFFSET + (possibleOrder - 1) * HORIZONTAL_SPACING;
            double expectedY = BASE_COORDINATE_OFFSET + (possibleLevel - 1) * VERTICAL_SPACING;
            
            boolean isValid = Math.abs(x - expectedX) < 1 && Math.abs(y - expectedY) < 1 && 
                             possibleLevel > 0 && possibleOrder > 0;
            
            if (!isValid) {
                System.out.println(String.format("   ⚠️  节点 %s: 实际(%.0f,%.0f) 期望(%.0f,%.0f) Level=%d Order=%d", 
                        nodeId, x, y, expectedX, expectedY, possibleLevel, possibleOrder));
            }
            
            return isValid;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 分析坐标计算精度
     */
    private static void analyzeCoordinatePrecision() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            System.out.println("📐 SQL坐标公式验证:");
            System.out.println("   公式: X = 150 + (level_order - 1) * 500");
            System.out.println("   公式: Y = 150 + (level_num - 1) * 400");
            System.out.println();
            
            int sampleCount = 0;
            Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
            while (fields.hasNext() && sampleCount < 10) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (isNodeObject(value)) {
                    JsonNode geometry = value.get("geometry");
                    if (geometry != null) {
                        double x = geometry.get("x").asDouble();
                        double y = geometry.get("y").asDouble();
                        
                        // 反推level和order
                        int level = (int) Math.round((y - BASE_COORDINATE_OFFSET) / VERTICAL_SPACING) + 1;
                        int order = (int) Math.round((x - BASE_COORDINATE_OFFSET) / HORIZONTAL_SPACING) + 1;
                        
                        System.out.println(String.format("   节点 %s: (%.0f,%.0f) -> Level=%d Order=%d", 
                                key, x, y, level, order));
                        sampleCount++;
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 分析坐标精度时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 验证层级排序逻辑
     */
    private static void validateLevelSorting() {
        try {
            File outputFile = new File(OUTPUT_FILE);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode outputJson = mapper.readTree(outputFile);
            
            System.out.println("📊 层级排序验证（SQL: ORDER BY id）:");
            
            // 按层级分组并验证排序
            for (int level = 1; level <= 5; level++) { // 检查前5个层级
                System.out.println(String.format("\n   Level %d 节点:", level));
                
                int expectedY = BASE_COORDINATE_OFFSET + (level - 1) * VERTICAL_SPACING;
                int nodeCountInLevel = 0;
                
                Iterator<Map.Entry<String, JsonNode>> fields = outputJson.fields();
                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> entry = fields.next();
                    String key = entry.getKey();
                    JsonNode value = entry.getValue();
                    
                    if (isNodeObject(value)) {
                        JsonNode geometry = value.get("geometry");
                        if (geometry != null) {
                            double y = geometry.get("y").asDouble();
                            double x = geometry.get("x").asDouble();
                            
                            // 检查是否属于当前层级
                            if (Math.abs(y - expectedY) < 1) {
                                int order = (int) Math.round((x - BASE_COORDINATE_OFFSET) / HORIZONTAL_SPACING) + 1;
                                System.out.println(String.format("     Order %d: %s (%.0f,%.0f)", 
                                        order, key, x, y));
                                nodeCountInLevel++;
                            }
                        }
                    }
                }
                
                if (nodeCountInLevel == 0) {
                    break; // 没有更多层级
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 验证层级排序时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为节点对象
     */
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
}
