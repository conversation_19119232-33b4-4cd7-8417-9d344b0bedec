package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 底部区域节点排序验证器
 * 验证孤立节点和结束节点的排序是否正确
 */
public class BottomAreaVerifier {
    
    public static void main(String[] args) {
        System.out.println("🔍 底部区域节点排序验证器");
        System.out.println("=".repeat(60));
        
        try {
            verifyBottomAreaSorting("src/main/java/org/example/test3/processDesignModel_bottom_sort_fix.json");
        } catch (Exception e) {
            System.err.println("❌ 验证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void verifyBottomAreaSorting(String filePath) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            System.out.println("⚠️ 文件不存在: " + filePath);
            return;
        }
        
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(file);
        
        // 收集所有节点信息
        List<NodeInfo> allNodes = new ArrayList<>();
        Map<String, List<String>> connections = new HashMap<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                NodeInfo node = parseNodeInfo(key, value);
                allNodes.add(node);
            } else if (isConnectionObject(value)) {
                parseConnection(value, connections);
            }
        }
        
        System.out.println("📊 节点统计:");
        System.out.println("   - 总节点数: " + allNodes.size());
        System.out.println("   - 总连线数: " + connections.size());
        
        // 分类节点
        List<NodeInfo> mainFlowNodes = new ArrayList<>();
        List<NodeInfo> isolatedNodes = new ArrayList<>();
        List<NodeInfo> endNodes = new ArrayList<>();
        
        for (NodeInfo node : allNodes) {
            if (isIsolatedNode(node, connections)) {
                isolatedNodes.add(node);
            } else if (isEndNode(node, connections)) {
                endNodes.add(node);
            } else {
                mainFlowNodes.add(node);
            }
        }
        
        System.out.println("\n📋 节点分类:");
        System.out.println("   - 主流程节点: " + mainFlowNodes.size());
        System.out.println("   - 孤立节点: " + isolatedNodes.size());
        System.out.println("   - 结束节点: " + endNodes.size());
        
        // 验证层级分布
        verifyLevelDistribution(mainFlowNodes, isolatedNodes, endNodes);
        
        // 验证Y坐标顺序
        verifyYCoordinateOrder(isolatedNodes, endNodes);
        
        // 输出详细信息
        printDetailedInfo(mainFlowNodes, isolatedNodes, endNodes);
    }
    
    private static void verifyLevelDistribution(List<NodeInfo> mainFlow, List<NodeInfo> isolated, List<NodeInfo> end) {
        System.out.println("\n🔍 层级分布验证:");
        
        // 计算各类节点的层级范围
        int maxMainLevel = mainFlow.stream().mapToInt(n -> n.levelNum).max().orElse(0);
        int minIsolatedLevel = isolated.stream().mapToInt(n -> n.levelNum).min().orElse(Integer.MAX_VALUE);
        int maxIsolatedLevel = isolated.stream().mapToInt(n -> n.levelNum).max().orElse(0);
        int minEndLevel = end.stream().mapToInt(n -> n.levelNum).min().orElse(Integer.MAX_VALUE);
        int maxEndLevel = end.stream().mapToInt(n -> n.levelNum).max().orElse(0);
        
        System.out.println("   主流程层级范围: 1 - " + maxMainLevel);
        if (!isolated.isEmpty()) {
            System.out.println("   孤立节点层级范围: " + minIsolatedLevel + " - " + maxIsolatedLevel);
        }
        if (!end.isEmpty()) {
            System.out.println("   结束节点层级范围: " + minEndLevel + " - " + maxEndLevel);
        }
        
        // 验证排序正确性
        boolean sortingCorrect = true;
        
        if (!isolated.isEmpty() && maxMainLevel >= minIsolatedLevel) {
            System.out.println("   ❌ 主流程节点层级与孤立节点重叠!");
            sortingCorrect = false;
        }
        
        if (!end.isEmpty() && !isolated.isEmpty() && maxIsolatedLevel >= minEndLevel) {
            System.out.println("   ❌ 孤立节点层级与结束节点重叠!");
            sortingCorrect = false;
        }
        
        if (sortingCorrect) {
            System.out.println("   ✅ 层级排序正确: 主流程 < 孤立节点 < 结束节点");
        }
    }
    
    private static void verifyYCoordinateOrder(List<NodeInfo> isolated, List<NodeInfo> end) {
        System.out.println("\n📐 Y坐标顺序验证:");
        
        if (isolated.isEmpty() || end.isEmpty()) {
            System.out.println("   ⚠️ 孤立节点或结束节点为空，跳过Y坐标验证");
            return;
        }
        
        double maxIsolatedY = isolated.stream().mapToDouble(n -> n.y).max().orElse(0);
        double minEndY = end.stream().mapToDouble(n -> n.y).min().orElse(Double.MAX_VALUE);
        
        System.out.println("   孤立节点最大Y坐标: " + maxIsolatedY);
        System.out.println("   结束节点最小Y坐标: " + minEndY);
        
        if (maxIsolatedY < minEndY) {
            System.out.println("   ✅ Y坐标顺序正确: 孤立节点在结束节点上方");
        } else {
            System.out.println("   ❌ Y坐标顺序错误: 结束节点不在孤立节点下方");
        }
    }
    
    private static void printDetailedInfo(List<NodeInfo> mainFlow, List<NodeInfo> isolated, List<NodeInfo> end) {
        System.out.println("\n📋 详细节点信息:");
        
        if (!isolated.isEmpty()) {
            System.out.println("   🏝️ 孤立节点:");
            isolated.stream()
                .sorted((a, b) -> Integer.compare(a.levelNum, b.levelNum))
                .forEach(node -> System.out.println(String.format("      Level %d: %s (Y=%.0f)", 
                    node.levelNum, node.id, node.y)));
        }
        
        if (!end.isEmpty()) {
            System.out.println("   🏁 结束节点:");
            end.stream()
                .sorted((a, b) -> Integer.compare(a.levelNum, b.levelNum))
                .forEach(node -> System.out.println(String.format("      Level %d: %s (Y=%.0f)", 
                    node.levelNum, node.id, node.y)));
        }
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && value.has("geometry");
    }
    
    private static boolean isConnectionObject(JsonNode value) {
        return value.has("source") && value.has("target");
    }
    
    private static boolean isIsolatedNode(NodeInfo node, Map<String, List<String>> connections) {
        // 检查是否有任何连接关系
        for (Map.Entry<String, List<String>> entry : connections.entrySet()) {
            if (entry.getKey().equals(node.id) || entry.getValue().contains(node.id)) {
                return false;
            }
        }
        return true;
    }
    
    private static boolean isEndNode(NodeInfo node, Map<String, List<String>> connections) {
        // nodeType=3的节点
        if (node.nodeType == 3) {
            return true;
        }
        
        // id="end"的节点
        if ("end".equals(node.id)) {
            return true;
        }
        
        // 出度为0且有入度的节点
        boolean hasOutgoing = connections.containsKey(node.id) && !connections.get(node.id).isEmpty();
        boolean hasIncoming = connections.values().stream().anyMatch(targets -> targets.contains(node.id));
        
        return !hasOutgoing && hasIncoming;
    }
    
    private static NodeInfo parseNodeInfo(String id, JsonNode nodeJson) {
        NodeInfo info = new NodeInfo();
        info.id = id;
        
        if (nodeJson.has("nodeName")) {
            info.nodeName = nodeJson.get("nodeName").asText();
        }
        
        if (nodeJson.has("nodeType")) {
            info.nodeType = nodeJson.get("nodeType").asInt();
        }
        
        if (nodeJson.has("level_num")) {
            info.levelNum = nodeJson.get("level_num").asInt();
        }
        
        if (nodeJson.has("geometry")) {
            JsonNode geometry = nodeJson.get("geometry");
            if (geometry.has("x")) {
                info.x = geometry.get("x").asDouble();
            }
            if (geometry.has("y")) {
                info.y = geometry.get("y").asDouble();
            }
        }
        
        return info;
    }
    
    private static void parseConnection(JsonNode connectionJson, Map<String, List<String>> connections) {
        if (connectionJson.has("source") && connectionJson.has("target")) {
            JsonNode source = connectionJson.get("source");
            JsonNode target = connectionJson.get("target");
            
            if (source.has("id") && target.has("id")) {
                String sourceId = source.get("id").asText();
                String targetId = target.get("id").asText();
                
                connections.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(targetId);
            }
        }
    }
    
    static class NodeInfo {
        String id;
        String nodeName;
        int nodeType;
        int levelNum;
        double x;
        double y;
    }
}
