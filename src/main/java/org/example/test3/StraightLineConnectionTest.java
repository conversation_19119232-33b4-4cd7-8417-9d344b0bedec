package org.example.test3;

/**
 * 直线连接测试程序
 * 测试特定连线的直线处理效果
 */
public class StraightLineConnectionTest {
    public static void main(String[] args) {
        System.out.println("📏 直线连接测试程序");
        System.out.println("=".repeat(80));
        System.out.println("测试目标：");
        System.out.println("1. 流程开始节点 -> 流程初始化节点：直线连接");
        System.out.println("2. 重新提交节点 -> byStart节点：直线连接");
        System.out.println("3. 其他连线保持智能避让路径");
        System.out.println("4. 验证mxGraph格式的abspoints数组");
        System.out.println();
        
        System.out.println("直线连接特性：");
        System.out.println("✅ 只包含起点和终点，无中间控制点");
        System.out.println("✅ 从源节点边缘连接到目标节点边缘");
        System.out.println("✅ 路径类型标记为'straight'");
        System.out.println("✅ 符合mxGraph标准格式");
        System.out.println();
        
        System.out.println("预期JSON格式：");
        System.out.println("```json");
        System.out.println("{");
        System.out.println("  \"geometry\": {");
        System.out.println("    \"x\": 0,");
        System.out.println("    \"y\": 0,");
        System.out.println("    \"width\": 0,");
        System.out.println("    \"height\": 0,");
        System.out.println("    \"relative\": 1,");
        System.out.println("    \"abspoints\": [");
        System.out.println("      {\"x\": 起点X, \"y\": 起点Y},");
        System.out.println("      {\"x\": 终点X, \"y\": 终点Y}");
        System.out.println("    ]");
        System.out.println("  }");
        System.out.println("}");
        System.out.println("```");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_straight_lines.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行直线连接处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 直线连接处理文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ 检测到流程开始->初始化连线的特殊处理");
                System.out.println("2. ✅ 检测到重新提交->byStart连线的特殊处理");
                System.out.println("3. ✅ 直线路径只包含2个abspoints点");
                System.out.println("4. ✅ 连接点位于节点边缘而非中心");
                
                System.out.println();
                System.out.println("🔍 调试信息验证：");
                System.out.println("- 检查控制台输出中的'检测到流程开始->初始化连线'");
                System.out.println("- 检查控制台输出中的'检测到重新提交->byStart连线'");
                System.out.println("- 确认'直线路径（特殊处理）'消息");
                System.out.println("- 验证'直线路径: (X,Y) -> (X,Y)'坐标信息");
                
                System.out.println();
                System.out.println("📋 预期效果对比：");
                System.out.println("修复前：");
                System.out.println("  - 连线可能包含多个控制点");
                System.out.println("  - 路径弯曲或包含避让点");
                System.out.println("  - abspoints数组包含3+个点");
                System.out.println();
                System.out.println("修复后：");
                System.out.println("  - 连线只包含起点和终点");
                System.out.println("  - 完全直线连接");
                System.out.println("  - abspoints数组只包含2个点");
                
                System.out.println();
                System.out.println("🔧 节点识别逻辑：");
                System.out.println("开始节点识别：");
                System.out.println("  - nodeType == 1");
                System.out.println("  - nodeName包含'开始'或'start'");
                System.out.println("  - nodeId包含'start'或'开始'");
                System.out.println();
                System.out.println("初始化节点识别：");
                System.out.println("  - nodeType == 2");
                System.out.println("  - nodeName包含'初始化'或'init'");
                System.out.println("  - nodeId包含'init'或'初始化'");
                System.out.println();
                System.out.println("重新提交节点识别：");
                System.out.println("  - nodeType == 4");
                System.out.println("  - nodeName包含'重新提交'或'recall'");
                System.out.println("  - nodeId包含'recall'或'重新提交'");
                
            } else {
                System.err.println("❌ 直线连接处理文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 直线连接测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 节点识别逻辑错误");
            System.err.println("   2. 连接点计算异常");
            System.err.println("   3. mxGraph几何信息生成错误");
            System.err.println("   4. JSON序列化问题");
        }
    }
}
