package org.example.test3;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * mxGraph连线几何信息类
 * 符合mxGraph标准格式的连线几何信息
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MxGraphConnectionGeometry {
    
    @JsonProperty("x")
    private double x = 0;
    
    @JsonProperty("y")
    private double y = 0;
    
    @JsonProperty("width")
    private double width = 0;
    
    @JsonProperty("height")
    private double height = 0;
    
    @JsonProperty("relative")
    private int relative = 1;
    
    @JsonProperty("abspoints")
    private List<AbsPoint> abspoints;
    
    public MxGraphConnectionGeometry() {}
    
    // Getter和Setter方法
    public double getX() {
        return x;
    }
    
    public void setX(double x) {
        this.x = x;
    }
    
    public double getY() {
        return y;
    }
    
    public void setY(double y) {
        this.y = y;
    }
    
    public double getWidth() {
        return width;
    }
    
    public void setWidth(double width) {
        this.width = width;
    }
    
    public double getHeight() {
        return height;
    }
    
    public void setHeight(double height) {
        this.height = height;
    }
    
    public int getRelative() {
        return relative;
    }
    
    public void setRelative(int relative) {
        this.relative = relative;
    }
    
    public List<AbsPoint> getAbspoints() {
        return abspoints;
    }
    
    public void setAbspoints(List<AbsPoint> abspoints) {
        this.abspoints = abspoints;
    }
    
    /**
     * 添加路径点
     */
    public void addAbsPoint(double x, double y) {
        if (abspoints == null) {
            abspoints = new ArrayList<>();
        }
        abspoints.add(new AbsPoint(x, y));
    }
    
    /**
     * 从ConnectionPoint列表设置abspoints
     */
    public void setAbspointsFromPath(List<FlowLayoutProcessor.ConnectionPoint> connectionPoints) {
        if (connectionPoints != null && !connectionPoints.isEmpty()) {
            this.abspoints = new ArrayList<>();
            for (FlowLayoutProcessor.ConnectionPoint cp : connectionPoints) {
                abspoints.add(new AbsPoint(cp.x, cp.y));
            }
        }
    }
    
    /**
     * 创建标准的两点连线（起点到终点）
     */
    public static MxGraphConnectionGeometry createSimpleLine(double startX, double startY, double endX, double endY) {
        MxGraphConnectionGeometry geometry = new MxGraphConnectionGeometry();
        geometry.addAbsPoint(startX, startY);
        geometry.addAbsPoint(endX, endY);
        return geometry;
    }
    
    /**
     * 创建带中间点的连线（避让路径）
     */
    public static MxGraphConnectionGeometry createAvoidancePath(double startX, double startY, 
                                                               double endX, double endY,
                                                               List<FlowLayoutProcessor.ConnectionPoint> intermediatePoints) {
        MxGraphConnectionGeometry geometry = new MxGraphConnectionGeometry();
        
        // 添加起点
        geometry.addAbsPoint(startX, startY);
        
        // 添加中间点
        if (intermediatePoints != null) {
            for (FlowLayoutProcessor.ConnectionPoint point : intermediatePoints) {
                geometry.addAbsPoint(point.x, point.y);
            }
        }
        
        // 添加终点
        geometry.addAbsPoint(endX, endY);
        
        return geometry;
    }
    
    @Override
    public String toString() {
        return String.format("MxGraphGeometry{x=%.1f, y=%.1f, w=%.1f, h=%.1f, rel=%d, abspoints=%d}", 
                x, y, width, height, relative,
                abspoints != null ? abspoints.size() : 0);
    }
    
    /**
     * mxGraph绝对坐标点
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AbsPoint {
        @JsonProperty("x")
        private double x;
        
        @JsonProperty("y")
        private double y;
        
        public AbsPoint() {}
        
        public AbsPoint(double x, double y) {
            this.x = x;
            this.y = y;
        }
        
        public double getX() {
            return x;
        }
        
        public void setX(double x) {
            this.x = x;
        }
        
        public double getY() {
            return y;
        }
        
        public void setY(double y) {
            this.y = y;
        }
        
        @Override
        public String toString() {
            return String.format("(%.1f,%.1f)", x, y);
        }
    }
}
