package org.example.test3;

/**
 * 底部区域节点排序修复测试程序
 * 确保底部区域节点按正确优先级排列：孤立节点 → 结束节点
 */
public class BottomAreaSortingFixTest {
    public static void main(String[] args) {
        System.out.println("🔄 底部区域节点排序修复测试程序");
        System.out.println("=".repeat(80));
        System.out.println("修复目标：");
        System.out.println("1. 孤立节点排在结束节点之前（Y坐标更小）");
        System.out.println("2. 层级分配顺序：主流程 → 孤立节点 → 结束节点");
        System.out.println("3. 确保层级连续性和逻辑正确性");
        System.out.println("4. 底部区域视觉效果清晰有序");
        System.out.println();
        
        System.out.println("修复前问题：");
        System.out.println("❌ 结束节点可能排在孤立节点之前");
        System.out.println("❌ 底部区域节点顺序混乱");
        System.out.println("❌ 不符合逻辑优先级");
        System.out.println();
        
        System.out.println("修复后效果：");
        System.out.println("✅ 孤立节点优先级高于结束节点");
        System.out.println("✅ 层级分配逻辑清晰");
        System.out.println("✅ 底部区域从上到下：孤立节点 → 结束节点");
        System.out.println("✅ 整体布局更加合理");
        System.out.println();
        
        System.out.println("预期层级分布：");
        System.out.println("Level 1-N:     主流程节点（开始、网关、审核等）");
        System.out.println("Level N+1:     第一个孤立节点");
        System.out.println("Level N+2:     第二个孤立节点");
        System.out.println("Level N+M:     最后一个孤立节点");
        System.out.println("Level N+M+1:   第一个结束节点");
        System.out.println("Level N+M+2:   第二个结束节点（如果有多个）");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_bottom_sort_fix.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行底部区域节点排序修复
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 底部区域节点排序修复文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 关键验证要点：");
                System.out.println("1. ✅ 层级顺序正确：主流程 < 孤立节点 < 结束节点");
                System.out.println("2. ✅ Y坐标递增：孤立节点Y < 结束节点Y");
                System.out.println("3. ✅ 层级连续性：无跳跃或重叠");
                System.out.println("4. ✅ 节点类型标识：正确区分各类节点");
                
                System.out.println();
                System.out.println("📋 验证方法：");
                System.out.println("1. 检查控制台输出中的'重新组织底部区域节点排序'部分");
                System.out.println("2. 确认孤立节点和结束节点的层级范围");
                System.out.println("3. 验证最终层级分布的合理性");
                System.out.println("4. 检查JSON文件中的level_num值");
                
                System.out.println();
                System.out.println("📐 坐标计算验证：");
                System.out.println("- 孤立节点Y坐标 = 150 + (level-1) * 400");
                System.out.println("- 结束节点Y坐标 = 150 + (level-1) * 400");
                System.out.println("- 确保结束节点Y > 孤立节点Y");
                
                System.out.println();
                System.out.println("🔍 调试信息关键点：");
                System.out.println("- '主流程最大层级' 的值");
                System.out.println("- '孤立节点层级范围' 的分配");
                System.out.println("- '结束节点层级范围' 的分配");
                System.out.println("- '底部区域节点排序' 的确认信息");
                
            } else {
                System.err.println("❌ 底部区域节点排序修复文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 底部区域节点排序修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 孤立节点识别逻辑错误");
            System.err.println("   2. 结束节点识别逻辑错误");
            System.err.println("   3. 层级重组算法异常");
            System.err.println("   4. 主流程最大层级计算错误");
        }
    }
}
