package org.example.test3;

/**
 * 连线修复测试程序
 * 验证智能连线布局的修复效果
 */
public class ConnectionFixTest {
    public static void main(String[] args) {
        System.out.println("🔧 连线修复测试程序");
        System.out.println("=".repeat(80));
        System.out.println("修复内容：");
        System.out.println("1. 修复连线几何信息未写入JSON的问题");
        System.out.println("2. 修复控制点设置方法的问题");
        System.out.println("3. 确保连线布局计算正确执行");
        System.out.println("4. 验证JSON输出包含连线几何信息");
        System.out.println();
        
        try {
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            String inputFile = "src/main/java/org/example/test3/processDesignModel.json";
            String outputFile = "src/main/java/org/example/test3/processDesignModel_connection_fixed.json";
            
            System.out.println("📁 输入文件: " + inputFile);
            System.out.println("📁 输出文件: " + outputFile);
            System.out.println();
            
            long startTime = System.currentTimeMillis();
            
            // 执行修复后的连线布局处理
            processor.processFlowLayout(inputFile, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println();
            System.out.println("⏱️ 处理耗时: " + (endTime - startTime) + " 毫秒");
            
            // 验证输出文件
            java.io.File output = new java.io.File(outputFile);
            if (output.exists()) {
                System.out.println("✅ 连线修复后的布局文件生成成功!");
                System.out.println("📊 文件大小: " + (output.length() / 1024) + " KB");
                
                System.out.println();
                System.out.println("🎯 修复验证要点：");
                System.out.println("1. ✅ 检查控制台是否显示'计算智能连线布局'");
                System.out.println("2. ✅ 检查控制台是否显示'更新连线 XXX 几何信息'");
                System.out.println("3. ✅ 检查JSON文件是否包含连线的geometry字段");
                System.out.println("4. ✅ 检查geometry字段是否包含startX/Y, endX/Y等");
                
                System.out.println();
                System.out.println("📋 预期JSON结构：");
                System.out.println("连线对象应包含：");
                System.out.println("```json");
                System.out.println("{");
                System.out.println("  \"source\": { \"id\": \"源节点ID\" },");
                System.out.println("  \"target\": { \"id\": \"目标节点ID\" },");
                System.out.println("  \"geometry\": {");
                System.out.println("    \"startX\": 数值,");
                System.out.println("    \"startY\": 数值,");
                System.out.println("    \"endX\": 数值,");
                System.out.println("    \"endY\": 数值,");
                System.out.println("    \"pathType\": \"curved\",");
                System.out.println("    \"controlPoints\": [");
                System.out.println("      { \"x\": 数值, \"y\": 数值 }");
                System.out.println("    ]");
                System.out.println("  }");
                System.out.println("}");
                System.out.println("```");
                
            } else {
                System.err.println("❌ 连线修复后的布局文件生成失败!");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 连线修复测试失败:");
            System.err.println("   错误类型: " + e.getClass().getSimpleName());
            System.err.println("   错误信息: " + e.getMessage());
            
            System.err.println("\n📋 错误堆栈:");
            e.printStackTrace();
            
            System.err.println("\n💡 可能的问题：");
            System.err.println("   1. 连线识别逻辑仍有问题");
            System.err.println("   2. 几何信息计算异常");
            System.err.println("   3. JSON序列化问题");
            System.err.println("   4. 控制点设置方法错误");
        }
    }
}
