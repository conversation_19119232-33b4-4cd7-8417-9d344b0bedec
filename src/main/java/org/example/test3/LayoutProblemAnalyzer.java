package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 布局问题分析器
 * 专门分析网关节点和结束节点的布局问题
 */
public class LayoutProblemAnalyzer {
    
    private static final String INPUT_FILE = "src/main/java/org/example/test3/processDesignModel.json";
    
    public static void main(String[] args) {
        System.out.println("🔍 布局问题分析器");
        System.out.println("=".repeat(60));
        
        try {
            LayoutProblemAnalyzer analyzer = new LayoutProblemAnalyzer();
            analyzer.analyzeLayoutProblems();
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void analyzeLayoutProblems() throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(INPUT_FILE));
        
        Map<String, JsonNode> allNodes = new HashMap<>();
        Map<String, JsonNode> allConnections = new HashMap<>();
        
        // 收集所有节点和连线
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                allNodes.put(key, value);
            } else if (isConnectionObject(value)) {
                allConnections.put(key, value);
            }
        }
        
        System.out.println("📊 基本统计:");
        System.out.println("   - 总节点数: " + allNodes.size());
        System.out.println("   - 总连线数: " + allConnections.size());
        
        // 分析节点类型分布
        analyzeNodeTypeDistribution(allNodes);
        
        // 分析网关节点
        analyzeGatewayNodes(allNodes, allConnections);
        
        // 分析结束节点
        analyzeEndNodes(allNodes, allConnections);
        
        // 分析连接关系
        analyzeConnectionPatterns(allNodes, allConnections);
    }
    
    private void analyzeNodeTypeDistribution(Map<String, JsonNode> nodes) {
        System.out.println("\n📋 节点类型详细分布:");
        
        Map<Integer, List<String>> typeGroups = new HashMap<>();
        
        for (Map.Entry<String, JsonNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            JsonNode node = entry.getValue();
            
            if (node.has("nodeType")) {
                int nodeType = node.get("nodeType").asInt();
                typeGroups.computeIfAbsent(nodeType, k -> new ArrayList<>()).add(nodeId);
            }
        }
        
        typeGroups.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int type = entry.getKey();
                List<String> nodeIds = entry.getValue();
                String typeName = getNodeTypeName(type);
                System.out.println(String.format("   - %s (类型%d): %d个", typeName, type, nodeIds.size()));
                
                // 显示节点详情
                for (String nodeId : nodeIds) {
                    JsonNode node = nodes.get(nodeId);
                    String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
                    System.out.println(String.format("     * %s: %s", nodeId, nodeName));
                }
            });
    }
    
    private void analyzeGatewayNodes(Map<String, JsonNode> nodes, Map<String, JsonNode> connections) {
        System.out.println("\n🚪 网关节点分析:");
        
        List<String> gatewayNodes = new ArrayList<>();
        for (Map.Entry<String, JsonNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            JsonNode node = entry.getValue();
            
            if (node.has("nodeType") && node.get("nodeType").asInt() == 13) {
                gatewayNodes.add(nodeId);
            }
        }
        
        System.out.println("   网关节点总数: " + gatewayNodes.size());
        
        for (String gatewayId : gatewayNodes) {
            JsonNode gateway = nodes.get(gatewayId);
            String nodeName = gateway.has("nodeName") ? gateway.get("nodeName").asText() : "无名称";
            
            // 分析网关节点的连接关系
            int incomingCount = 0;
            int outgoingCount = 0;
            List<String> incomingNodes = new ArrayList<>();
            List<String> outgoingNodes = new ArrayList<>();
            
            for (JsonNode connection : connections.values()) {
                if (connection.has("source") && connection.get("source").has("id") &&
                    gatewayId.equals(connection.get("source").get("id").asText())) {
                    outgoingCount++;
                    if (connection.has("target") && connection.get("target").has("id")) {
                        outgoingNodes.add(connection.get("target").get("id").asText());
                    }
                }
                
                if (connection.has("target") && connection.get("target").has("id") &&
                    gatewayId.equals(connection.get("target").get("id").asText())) {
                    incomingCount++;
                    if (connection.has("source") && connection.get("source").has("id")) {
                        incomingNodes.add(connection.get("source").get("id").asText());
                    }
                }
            }
            
            System.out.println(String.format("   - 网关节点: %s (%s)", gatewayId, nodeName));
            System.out.println(String.format("     入度: %d, 出度: %d", incomingCount, outgoingCount));
            System.out.println("     入口节点: " + String.join(", ", incomingNodes));
            System.out.println("     出口节点: " + String.join(", ", outgoingNodes));
            
            // 检查原始坐标
            if (gateway.has("geometry")) {
                JsonNode geometry = gateway.get("geometry");
                double x = geometry.has("x") ? geometry.get("x").asDouble() : 0;
                double y = geometry.has("y") ? geometry.get("y").asDouble() : 0;
                System.out.println(String.format("     原始坐标: (%.0f, %.0f)", x, y));
            }
        }
    }
    
    private void analyzeEndNodes(Map<String, JsonNode> nodes, Map<String, JsonNode> connections) {
        System.out.println("\n🏁 结束节点分析:");
        
        // 查找可能的结束节点
        List<String> endNodeCandidates = new ArrayList<>();
        
        // 1. 查找nodeType=3的节点
        for (Map.Entry<String, JsonNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            JsonNode node = entry.getValue();
            
            if (node.has("nodeType") && node.get("nodeType").asInt() == 3) {
                endNodeCandidates.add(nodeId);
            }
        }
        
        // 2. 查找id="end"的节点
        if (nodes.containsKey("end")) {
            if (!endNodeCandidates.contains("end")) {
                endNodeCandidates.add("end");
            }
        }
        
        // 3. 查找出度为0的节点（可能是结束节点）
        Set<String> nodesWithOutgoing = new HashSet<>();
        for (JsonNode connection : connections.values()) {
            if (connection.has("source") && connection.get("source").has("id")) {
                nodesWithOutgoing.add(connection.get("source").get("id").asText());
            }
        }
        
        List<String> zeroOutDegreeNodes = new ArrayList<>();
        for (String nodeId : nodes.keySet()) {
            if (!nodesWithOutgoing.contains(nodeId)) {
                zeroOutDegreeNodes.add(nodeId);
            }
        }
        
        System.out.println("   结束节点候选 (nodeType=3): " + endNodeCandidates.size());
        System.out.println("   出度为0的节点: " + zeroOutDegreeNodes.size());
        
        // 分析结束节点候选
        for (String endId : endNodeCandidates) {
            JsonNode endNode = nodes.get(endId);
            String nodeName = endNode.has("nodeName") ? endNode.get("nodeName").asText() : "无名称";
            int nodeType = endNode.has("nodeType") ? endNode.get("nodeType").asInt() : -1;
            
            System.out.println(String.format("   - 结束节点: %s (类型%d) - %s", endId, nodeType, nodeName));
            
            // 检查入度
            int incomingCount = 0;
            List<String> incomingNodes = new ArrayList<>();
            
            for (JsonNode connection : connections.values()) {
                if (connection.has("target") && connection.get("target").has("id") &&
                    endId.equals(connection.get("target").get("id").asText())) {
                    incomingCount++;
                    if (connection.has("source") && connection.get("source").has("id")) {
                        incomingNodes.add(connection.get("source").get("id").asText());
                    }
                }
            }
            
            System.out.println(String.format("     入度: %d", incomingCount));
            System.out.println("     前置节点: " + String.join(", ", incomingNodes));
            
            // 检查原始坐标
            if (endNode.has("geometry")) {
                JsonNode geometry = endNode.get("geometry");
                double x = geometry.has("x") ? geometry.get("x").asDouble() : 0;
                double y = geometry.has("y") ? geometry.get("y").asDouble() : 0;
                System.out.println(String.format("     原始坐标: (%.0f, %.0f)", x, y));
            }
        }
        
        System.out.println("\n   出度为0的节点详情:");
        for (String nodeId : zeroOutDegreeNodes) {
            JsonNode node = nodes.get(nodeId);
            String nodeName = node.has("nodeName") ? node.get("nodeName").asText() : "无名称";
            int nodeType = node.has("nodeType") ? node.get("nodeType").asInt() : -1;
            System.out.println(String.format("     - %s (类型%d): %s", nodeId, nodeType, nodeName));
        }
    }
    
    private void analyzeConnectionPatterns(Map<String, JsonNode> nodes, Map<String, JsonNode> connections) {
        System.out.println("\n🔗 连接模式分析:");
        
        // 统计各种连接模式
        int validConnections = 0;
        int invalidConnections = 0;
        Map<String, Integer> sourceTypeCount = new HashMap<>();
        Map<String, Integer> targetTypeCount = new HashMap<>();
        
        for (JsonNode connection : connections.values()) {
            String sourceId = null;
            String targetId = null;
            
            if (connection.has("source") && connection.get("source").has("id")) {
                sourceId = connection.get("source").get("id").asText();
            }
            
            if (connection.has("target") && connection.get("target").has("id")) {
                targetId = connection.get("target").get("id").asText();
            }
            
            if (sourceId != null && targetId != null && 
                nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                
                validConnections++;
                
                // 统计源节点类型
                JsonNode sourceNode = nodes.get(sourceId);
                if (sourceNode.has("nodeType")) {
                    int sourceType = sourceNode.get("nodeType").asInt();
                    String sourceTypeName = getNodeTypeName(sourceType);
                    sourceTypeCount.put(sourceTypeName, sourceTypeCount.getOrDefault(sourceTypeName, 0) + 1);
                }
                
                // 统计目标节点类型
                JsonNode targetNode = nodes.get(targetId);
                if (targetNode.has("nodeType")) {
                    int targetType = targetNode.get("nodeType").asInt();
                    String targetTypeName = getNodeTypeName(targetType);
                    targetTypeCount.put(targetTypeName, targetTypeCount.getOrDefault(targetTypeName, 0) + 1);
                }
                
            } else {
                invalidConnections++;
            }
        }
        
        System.out.println("   有效连线: " + validConnections);
        System.out.println("   无效连线: " + invalidConnections);
        
        System.out.println("\n   作为源节点的类型分布:");
        sourceTypeCount.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> System.out.println("     " + entry.getKey() + ": " + entry.getValue()));
        
        System.out.println("\n   作为目标节点的类型分布:");
        targetTypeCount.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> System.out.println("     " + entry.getKey() + ": " + entry.getValue()));
    }
    
    private boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && 
               value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private String getNodeTypeName(int nodeType) {
        switch (nodeType) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 3: return "结束节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            case 99: return "连线";
            default: return "未知类型(" + nodeType + ")";
        }
    }
}
