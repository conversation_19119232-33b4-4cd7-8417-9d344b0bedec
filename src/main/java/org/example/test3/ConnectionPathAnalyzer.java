package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.*;

/**
 * 连线路径分析器
 * 专门分析特定连线的路径点和几何信息
 */
public class ConnectionPathAnalyzer {
    
    public static void main(String[] args) {
        System.out.println("🔍 连线路径分析器");
        System.out.println("=".repeat(80));
        
        try {
            analyzeSpecificConnections("src/main/java/org/example/test3/processDesignModel.json");
        } catch (Exception e) {
            System.err.println("❌ 分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void analyzeSpecificConnections(String filePath) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(new File(filePath));
        
        // 收集所有节点和连线信息
        Map<String, NodeInfo> nodes = new HashMap<>();
        List<ConnectionInfo> connections = new ArrayList<>();
        
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            
            if (isNodeObject(value)) {
                NodeInfo node = parseNodeInfo(key, value);
                nodes.put(key, node);
            } else if (isConnectionObject(value)) {
                ConnectionInfo connection = parseConnectionInfo(key, value);
                if (connection != null) {
                    connections.add(connection);
                }
            }
        }
        
        System.out.println("📊 基本统计:");
        System.out.println("   - 节点数量: " + nodes.size());
        System.out.println("   - 连线数量: " + connections.size());
        
        // 查找特定连线
        System.out.println("\n🎯 查找目标连线:");
        
        // 1. 查找流程开始到流程初始化的连线
        ConnectionInfo startToInitConnection = findStartToInitConnection(connections, nodes);
        if (startToInitConnection != null) {
            analyzeConnectionPath("流程开始 -> 流程初始化", startToInitConnection, nodes);
        } else {
            System.out.println("   ⚠️ 未找到流程开始到流程初始化的连线");
        }
        
        System.out.println();
        
        // 2. 查找重新提交到byStart的连线
        ConnectionInfo recallToByStartConnection = findRecallToByStartConnection(connections, nodes);
        if (recallToByStartConnection != null) {
            analyzeConnectionPath("重新提交 -> byStart", recallToByStartConnection, nodes);
        } else {
            System.out.println("   ⚠️ 未找到重新提交到byStart的连线");
        }
        
        // 3. 分析所有非直线连线
        System.out.println("\n📋 所有非直线连线分析:");
        analyzeAllNonStraightConnections(connections, nodes);
    }
    
    private static ConnectionInfo findStartToInitConnection(List<ConnectionInfo> connections, Map<String, NodeInfo> nodes) {
        System.out.println("   🔍 查找流程开始 -> 流程初始化连线...");
        
        for (ConnectionInfo conn : connections) {
            NodeInfo sourceNode = nodes.get(conn.sourceId);
            NodeInfo targetNode = nodes.get(conn.targetId);
            
            if (sourceNode != null && targetNode != null) {
                // 检查是否为开始节点到初始化节点
                boolean isStartNode = isStartNode(sourceNode);
                boolean isInitNode = isInitNode(targetNode);
                
                if (isStartNode && isInitNode) {
                    System.out.println(String.format("   ✅ 找到连线: %s (%s) -> %s (%s)", 
                        conn.sourceId, sourceNode.nodeName,
                        conn.targetId, targetNode.nodeName));
                    return conn;
                }
            }
        }
        
        return null;
    }
    
    private static ConnectionInfo findRecallToByStartConnection(List<ConnectionInfo> connections, Map<String, NodeInfo> nodes) {
        System.out.println("   🔍 查找重新提交 -> byStart连线...");
        
        for (ConnectionInfo conn : connections) {
            NodeInfo sourceNode = nodes.get(conn.sourceId);
            NodeInfo targetNode = nodes.get(conn.targetId);
            
            if (sourceNode != null && targetNode != null) {
                // 检查是否为recall节点到byStart节点
                boolean isRecallNode = isRecallNode(sourceNode);
                boolean isByStartNode = "byStart".equals(conn.targetId) || 
                                       (targetNode.nodeName != null && targetNode.nodeName.contains("byStart"));
                
                if (isRecallNode && isByStartNode) {
                    System.out.println(String.format("   ✅ 找到连线: %s (%s) -> %s (%s)", 
                        conn.sourceId, sourceNode.nodeName,
                        conn.targetId, targetNode.nodeName));
                    return conn;
                }
            }
        }
        
        return null;
    }
    
    private static void analyzeConnectionPath(String description, ConnectionInfo connection, Map<String, NodeInfo> nodes) {
        System.out.println(String.format("\n📐 分析连线: %s", description));
        System.out.println(String.format("   连线ID: %s", connection.id));
        System.out.println(String.format("   源节点: %s -> 目标节点: %s", connection.sourceId, connection.targetId));
        
        NodeInfo sourceNode = nodes.get(connection.sourceId);
        NodeInfo targetNode = nodes.get(connection.targetId);
        
        if (sourceNode != null && targetNode != null) {
            System.out.println(String.format("   源节点坐标: (%.1f, %.1f)", sourceNode.x, sourceNode.y));
            System.out.println(String.format("   目标节点坐标: (%.1f, %.1f)", targetNode.x, targetNode.y));
            
            // 计算直线距离
            double distance = Math.sqrt(Math.pow(targetNode.x - sourceNode.x, 2) + 
                                      Math.pow(targetNode.y - sourceNode.y, 2));
            System.out.println(String.format("   直线距离: %.1f像素", distance));
        }
        
        // 分析abspoints
        if (connection.hasGeometry && connection.abspoints != null && !connection.abspoints.isEmpty()) {
            System.out.println(String.format("   路径点数量: %d", connection.abspoints.size()));
            System.out.println("   详细路径点:");
            
            for (int i = 0; i < connection.abspoints.size(); i++) {
                Point point = connection.abspoints.get(i);
                String pointType = "";
                if (i == 0) pointType = " (起点)";
                else if (i == connection.abspoints.size() - 1) pointType = " (终点)";
                else pointType = " (控制点)";
                
                System.out.println(String.format("      点%d: (%.1f, %.1f)%s", i + 1, point.x, point.y, pointType));
            }
            
            // 判断是否为直线
            boolean isStraightLine = isConnectionStraightLine(connection);
            System.out.println(String.format("   是否为直线: %s", isStraightLine ? "是" : "否"));
            
            if (!isStraightLine) {
                analyzeWhyNotStraight(connection, sourceNode, targetNode);
            }
        } else {
            System.out.println("   ⚠️ 无几何信息或路径点");
        }
    }
    
    private static void analyzeWhyNotStraight(ConnectionInfo connection, NodeInfo sourceNode, NodeInfo targetNode) {
        System.out.println("   🔍 非直线原因分析:");
        
        if (connection.abspoints.size() > 2) {
            System.out.println("      - 包含中间控制点，可能原因：");
            System.out.println("        * 避让其他节点");
            System.out.println("        * mxGraph自动路径规划");
            System.out.println("        * 手动调整的路径");
            
            // 分析中间点的位置
            for (int i = 1; i < connection.abspoints.size() - 1; i++) {
                Point controlPoint = connection.abspoints.get(i);
                System.out.println(String.format("        * 控制点%d偏移: X方向%.1f, Y方向%.1f", 
                    i, Math.abs(controlPoint.x - sourceNode.x), Math.abs(controlPoint.y - sourceNode.y)));
            }
        }
        
        // 检查起点和终点是否在节点中心
        Point startPoint = connection.abspoints.get(0);
        Point endPoint = connection.abspoints.get(connection.abspoints.size() - 1);
        
        boolean startAtCenter = Math.abs(startPoint.x - sourceNode.x) < 10 && 
                               Math.abs(startPoint.y - sourceNode.y) < 10;
        boolean endAtCenter = Math.abs(endPoint.x - targetNode.x) < 10 && 
                             Math.abs(endPoint.y - targetNode.y) < 10;
        
        System.out.println(String.format("      - 起点是否在源节点中心: %s", startAtCenter ? "是" : "否"));
        System.out.println(String.format("      - 终点是否在目标节点中心: %s", endAtCenter ? "是" : "否"));
        
        if (!startAtCenter || !endAtCenter) {
            System.out.println("        * 连接点不在节点中心，可能是边缘连接");
        }
    }
    
    private static void analyzeAllNonStraightConnections(List<ConnectionInfo> connections, Map<String, NodeInfo> nodes) {
        int nonStraightCount = 0;
        
        for (ConnectionInfo conn : connections) {
            if (conn.hasGeometry && !isConnectionStraightLine(conn)) {
                nonStraightCount++;
                
                if (nonStraightCount <= 5) { // 只显示前5个
                    NodeInfo sourceNode = nodes.get(conn.sourceId);
                    NodeInfo targetNode = nodes.get(conn.targetId);
                    
                    System.out.println(String.format("   %d. %s -> %s (%d个路径点)", 
                        nonStraightCount, 
                        sourceNode != null ? sourceNode.nodeName : conn.sourceId,
                        targetNode != null ? targetNode.nodeName : conn.targetId,
                        conn.abspoints.size()));
                }
            }
        }
        
        System.out.println(String.format("   总计发现 %d 条非直线连线", nonStraightCount));
    }
    
    private static boolean isConnectionStraightLine(ConnectionInfo connection) {
        if (connection.abspoints == null || connection.abspoints.size() <= 2) {
            return true; // 两点或更少认为是直线
        }
        
        // 检查所有中间点是否在起点和终点的直线上
        Point start = connection.abspoints.get(0);
        Point end = connection.abspoints.get(connection.abspoints.size() - 1);
        
        for (int i = 1; i < connection.abspoints.size() - 1; i++) {
            Point middle = connection.abspoints.get(i);
            
            // 计算点到直线的距离
            double distance = pointToLineDistance(middle, start, end);
            if (distance > 5) { // 允许5像素的误差
                return false;
            }
        }
        
        return true;
    }
    
    private static double pointToLineDistance(Point point, Point lineStart, Point lineEnd) {
        double A = lineEnd.y - lineStart.y;
        double B = lineStart.x - lineEnd.x;
        double C = lineEnd.x * lineStart.y - lineStart.x * lineEnd.y;
        
        return Math.abs(A * point.x + B * point.y + C) / Math.sqrt(A * A + B * B);
    }
    
    private static boolean isStartNode(NodeInfo node) {
        return (node.nodeType == 1) || 
               (node.nodeName != null && (node.nodeName.contains("开始") || node.nodeName.contains("start")));
    }
    
    private static boolean isInitNode(NodeInfo node) {
        return (node.nodeType == 2) || 
               (node.nodeName != null && (node.nodeName.contains("初始化") || node.nodeName.contains("init")));
    }
    
    private static boolean isRecallNode(NodeInfo node) {
        return (node.nodeType == 4) || 
               (node.nodeName != null && (node.nodeName.contains("重新提交") || node.nodeName.contains("recall")));
    }
    
    private static boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") && value.has("geometry") && 
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }
    
    private static boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }
    
    private static NodeInfo parseNodeInfo(String id, JsonNode nodeJson) {
        NodeInfo info = new NodeInfo();
        info.id = id;
        
        if (nodeJson.has("nodeName")) {
            info.nodeName = nodeJson.get("nodeName").asText();
        }
        
        if (nodeJson.has("nodeType")) {
            info.nodeType = nodeJson.get("nodeType").asInt();
        }
        
        if (nodeJson.has("geometry")) {
            JsonNode geometry = nodeJson.get("geometry");
            if (geometry.has("x")) info.x = geometry.get("x").asDouble();
            if (geometry.has("y")) info.y = geometry.get("y").asDouble();
        }
        
        return info;
    }
    
    private static ConnectionInfo parseConnectionInfo(String id, JsonNode connJson) {
        ConnectionInfo info = new ConnectionInfo();
        info.id = id;
        
        // 解析source和target
        if (connJson.has("source") && connJson.get("source").has("id")) {
            info.sourceId = connJson.get("source").get("id").asText();
        }
        if (connJson.has("target") && connJson.get("target").has("id")) {
            info.targetId = connJson.get("target").get("id").asText();
        }
        
        // 解析几何信息
        if (connJson.has("geometry")) {
            JsonNode geometry = connJson.get("geometry");
            info.hasGeometry = true;
            
            if (geometry.has("abspoints") && geometry.get("abspoints").isArray()) {
                JsonNode abspoints = geometry.get("abspoints");
                info.abspoints = new ArrayList<>();
                
                for (JsonNode pointNode : abspoints) {
                    if (pointNode.has("x") && pointNode.has("y")) {
                        info.abspoints.add(new Point(
                            pointNode.get("x").asDouble(),
                            pointNode.get("y").asDouble()
                        ));
                    }
                }
            }
        }
        
        return info.sourceId != null && info.targetId != null ? info : null;
    }
    
    // 数据结构
    static class NodeInfo {
        String id;
        String nodeName;
        int nodeType;
        double x, y;
    }
    
    static class ConnectionInfo {
        String id;
        String sourceId, targetId;
        boolean hasGeometry = false;
        List<Point> abspoints;
    }
    
    static class Point {
        double x, y;
        Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
    }
}
